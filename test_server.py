from wsgiref.simple_server import make_server

def simple_app(environ, start_response):
    status = '200 OK'
    headers = [('Content-Type', 'text/html; charset=utf-8')]
    start_response(status, headers)
    
    html = """
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>اختبار الخادم</title>
    </head>
    <body>
        <h1>الخادم يعمل بنجاح!</h1>
        <p>هذا اختبار للتأكد من عمل الخادم</p>
    </body>
    </html>
    """
    
    return [html.encode('utf-8')]

if __name__ == '__main__':
    print("Starting test server...")
    httpd = make_server('localhost', 8001, simple_app)
    print("Server running on http://localhost:8001")
    httpd.serve_forever()
