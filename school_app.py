from wsgiref.simple_server import make_server
import json
import os
import html

# بيانات مؤقتة (بدلاً من قاعدة البيانات)
STUDENTS = []
TEACHERS = []
CLASSES = []

# تحميل البيانات من ملفات JSON إذا كانت موجودة
def load_data():
    global STUDENTS, TEACHERS, CLASSES
    if os.path.exists('students.json'):
        with open('students.json', 'r', encoding='utf-8') as f:
            STUDENTS = json.load(f)
    if os.path.exists('teachers.json'):
        with open('teachers.json', 'r', encoding='utf-8') as f:
            TEACHERS = json.load(f)
    if os.path.exists('classes.json'):
        with open('classes.json', 'r', encoding='utf-8') as f:
            CLASSES = json.load(f)

# حفظ البيانات إلى ملفات JSON
def save_data():
    with open('students.json', 'w', encoding='utf-8') as f:
        json.dump(STUDENTS, f, ensure_ascii=False)
    with open('teachers.json', 'w', encoding='utf-8') as f:
        json.dump(TEACHERS, f, ensure_ascii=False)
    with open('classes.json', 'w', encoding='utf-8') as f:
        json.dump(CLASSES, f, ensure_ascii=False)

# تحميل البيانات عند بدء التطبيق
load_data()

# قالب HTML الرئيسي
HTML_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المدرسة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .content {
            padding: 20px;
        }
        .nav-link {
            color: rgba(255,255,255,.75);
        }
        .nav-link:hover {
            color: white;
        }
        .active {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h3 class="text-light">نظام إدارة المدرسة</h3>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" href="/">
                                <i class="bi bi-house-door me-2"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" href="/students">
                                <i class="bi bi-mortarboard me-2"></i>
                                الطلاب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" href="/teachers">
                                <i class="bi bi-person me-2"></i>
                                المعلمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" href="/classes">
                                <i class="bi bi-book me-2"></i>
                                الفصول الدراسية
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                {content}
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

# صفحات التطبيق
def home_page():
    content = """
    <div class="container mt-5">
        <div class="jumbotron bg-light p-5 rounded">
            <h1 class="display-4">مرحباً بك في نظام إدارة المدرسة</h1>
            <p class="lead">هذا النظام يساعدك على إدارة الطلاب والمعلمين والفصول الدراسية بكل سهولة.</p>
            <hr class="my-4">
            <div class="row mt-5">
                <div class="col-md-4">
                    <div class="card text-center mb-4">
                        <div class="card-body">
                            <h5 class="card-title"><i class="bi bi-mortarboard fs-1 text-primary"></i></h5>
                            <h2 class="card-text">{students_count}</h2>
                            <p class="card-text">طالب</p>
                            <a href="/students" class="btn btn-primary">إدارة الطلاب</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center mb-4">
                        <div class="card-body">
                            <h5 class="card-title"><i class="bi bi-person fs-1 text-success"></i></h5>
                            <h2 class="card-text">{teachers_count}</h2>
                            <p class="card-text">معلم</p>
                            <a href="/teachers" class="btn btn-success">إدارة المعلمين</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center mb-4">
                        <div class="card-body">
                            <h5 class="card-title"><i class="bi bi-book fs-1 text-warning"></i></h5>
                            <h2 class="card-text">{classes_count}</h2>
                            <p class="card-text">فصل دراسي</p>
                            <a href="/classes" class="btn btn-warning">إدارة الفصول</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    """.format(
        students_count=len(STUDENTS),
        teachers_count=len(TEACHERS),
        classes_count=len(CLASSES)
    )
    return HTML_TEMPLATE.format(content=content)

def students_page(query_params=None):
    # إضافة طالب جديد
    if query_params and 'action' in query_params and query_params['action'] == 'add':
        new_student = {
            'id': len(STUDENTS) + 1,
            'name': query_params.get('name', ''),
            'grade': query_params.get('grade', ''),
            'age': query_params.get('age', ''),
            'parent_phone': query_params.get('parent_phone', '')
        }
        STUDENTS.append(new_student)
        save_data()
    
    # حذف طالب
    if query_params and 'action' in query_params and query_params['action'] == 'delete':
        student_id = int(query_params.get('id', 0))
        for i, student in enumerate(STUDENTS):
            if student['id'] == student_id:
                STUDENTS.pop(i)
                break
        save_data()
    
    students_list = ""
    for student in STUDENTS:
        students_list += f"""
        <tr>
            <td>{student['id']}</td>
            <td>{html.escape(student['name'])}</td>
            <td>{html.escape(student['grade'])}</td>
            <td>{html.escape(student['age'])}</td>
            <td>{html.escape(student['parent_phone'])}</td>
            <td>
                <a href="/students?action=delete&id={student['id']}" class="btn btn-sm btn-danger">حذف</a>
            </td>
        </tr>
        """
    
    content = """
    <div class="container mt-4">
        <h2>إدارة الطلاب</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                إضافة طالب جديد
            </div>
            <div class="card-body">
                <form action="/students" method="get">
                    <input type="hidden" name="action" value="add">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="name" class="form-label">اسم الطالب</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="grade" class="form-label">الصف</label>
                            <input type="text" class="form-control" id="grade" name="grade" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="age" class="form-label">العمر</label>
                            <input type="number" class="form-control" id="age" name="age" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="parent_phone" class="form-label">هاتف ولي الأمر</label>
                            <input type="text" class="form-control" id="parent_phone" name="parent_phone" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                قائمة الطلاب
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>الاسم</th>
                                <th>الصف</th>
                                <th>العمر</th>
                                <th>هاتف ولي الأمر</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {students_list}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    """.format(students_list=students_list)
    
    return HTML_TEMPLATE.format(content=content)

def teachers_page(query_params=None):
    # إضافة معلم جديد
    if query_params and 'action' in query_params and query_params['action'] == 'add':
        new_teacher = {
            'id': len(TEACHERS) + 1,
            'name': query_params.get('name', ''),
            'subject': query_params.get('subject', ''),
            'phone': query_params.get('phone', ''),
            'email': query_params.get('email', '')
        }
        TEACHERS.append(new_teacher)
        save_data()
    
    # حذف معلم
    if query_params and 'action' in query_params and query_params['action'] == 'delete':
        teacher_id = int(query_params.get('id', 0))
        for i, teacher in enumerate(TEACHERS):
            if teacher['id'] == teacher_id:
                TEACHERS.pop(i)
                break
        save_data()
    
    teachers_list = ""
    for teacher in TEACHERS:
        teachers_list += f"""
        <tr>
            <td>{teacher['id']}</td>
            <td>{html.escape(teacher['name'])}</td>
            <td>{html.escape(teacher['subject'])}</td>
            <td>{html.escape(teacher['phone'])}</td>
            <td>{html.escape(teacher['email'])}</td>
            <td>
                <a href="/teachers?action=delete&id={teacher['id']}" class="btn btn-sm btn-danger">حذف</a>
            </td>
        </tr>
        """
    
    content = """
    <div class="container mt-4">
        <h2>إدارة المعلمين</h2>
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                إضافة معلم جديد
            </div>
            <div class="card-body">
                <form action="/teachers" method="get">
                    <input type="hidden" name="action" value="add">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="name" class="form-label">اسم المعلم</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="subject" class="form-label">المادة</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phone" name="phone" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success">إضافة</button>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-success text-white">
                قائمة المعلمين
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>الاسم</th>
                                <th>المادة</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {teachers_list}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    """.format(teachers_list=teachers_list)
    
    return HTML_TEMPLATE.format(content=content)

def classes_page(query_params=None):
    # إضافة فصل جديد
    if query_params and 'action' in query_params and query_params['action'] == 'add':
        new_class = {
            'id': len(CLASSES) + 1,
            'name': query_params.get('name', ''),
            'grade': query_params.get('grade', ''),
            'teacher': query_params.get('teacher', ''),
            'room': query_params.get('room', '')
        }
        CLASSES.append(new_class)
        save_data()
    
    # حذف فصل
    if query_params and 'action' in query_params and query_params['action'] == 'delete':
        class_id = int(query_params.get('id', 0))
        for i, class_item in enumerate(CLASSES):
            if class_item['id'] == class_id:
                CLASSES.pop(i)
                break
        save_data()
    
    classes_list = ""
    for class_item in CLASSES:
        classes_list += f"""
        <tr>
            <td>{class_item['id']}</td>
            <td>{html.escape(class_item['name'])}</td>
            <td>{html.escape(class_item['grade'])}</td>
            <td>{html.escape(class_item['teacher'])}</td>
            <td>{html.escape(class_item['room'])}</td>
            <td>
                <a href="/classes?action=delete&id={class_item['id']}" class="btn btn-sm btn-danger">حذف</a>
            </td>
        </tr>
        """
    
    # قائمة المعلمين للاختيار
    teacher_options = ""
    for teacher in TEACHERS:
        teacher_options += f'<option value="{html.escape(teacher["name"])}">{html.escape(teacher["name"])}</option>'
    
    content = """
    <div class="container mt-4">
        <h2>إدارة الفصول الدراسية</h2>
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                إضافة فصل جديد
            </div>
            <div class="card-body">
                <form action="/classes" method="get">
                    <input type="hidden" name="action" value="add">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="name" class="form-label">اسم الفصل</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="grade" class="form-label">المرحلة الدراسية</label>
                            <input type="text" class="form-control" id="grade" name="grade" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="teacher" class="form-label">المعلم المسؤول</label>
                            <select class="form-select" id="teacher" name="teacher" required>
                                <option value="">اختر المعلم</option>
                                {teacher_options}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="room" class="form-label">رقم القاعة</label>
                            <input type="text" class="form-control" id="room" name="room" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-warning">إضافة</button>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-warning text-dark">
                قائمة الفصول الدراسية
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الفصل</th>
                                <th>المرحلة الدراسية</th>
                                <th>المعلم المسؤول</th>
                                <th>رقم القاعة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {classes_list}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    """.format(classes_list=classes_list, teacher_options=teacher_options)
    
    return HTML_TEMPLATE.format(content=content)

# تحليل المعاملات من URL
def parse_query_string(query_string):
    params = {}
    if query_string:
        for param in query_string.split('&'):
            if '=' in param:
                key, value = param.split('=', 1)
                # فك تشفير URL
                import urllib.parse
                params[urllib.parse.unquote_plus(key)] = urllib.parse.unquote_plus(value)
    return params

# تطبيق WSGI الرئيسي
def application(environ, start_response):
    path = environ.get('PATH_INFO', '/')
    query_string = environ.get('QUERY_STRING', '')
    query_params = parse_query_string(query_string)

    # تحديد المحتوى بناءً على المسار
    if path == '/':
        response_body = home_page()
    elif path == '/students':
        response_body = students_page(query_params)
    elif path == '/teachers':
        response_body = teachers_page(query_params)
    elif path == '/classes':
        response_body = classes_page(query_params)
    else:
        # صفحة 404
        response_body = HTML_TEMPLATE.format(content="""
        <div class="container mt-5">
            <div class="alert alert-danger text-center">
                <h1>404 - الصفحة غير موجودة</h1>
                <p>عذراً، الصفحة التي تبحث عنها غير موجودة.</p>
                <a href="/" class="btn btn-primary">العودة للرئيسية</a>
            </div>
        </div>
        """)

    # إعداد الاستجابة
    response_body = response_body.encode('utf-8')
    status = '200 OK'
    headers = [
        ('Content-Type', 'text/html; charset=utf-8'),
        ('Content-Length', str(len(response_body)))
    ]

    start_response(status, headers)
    return [response_body]

# تشغيل الخادم
if __name__ == '__main__':
    print("بدء تشغيل خادم نظام إدارة المدرسة...")
    print("يمكنك الوصول للتطبيق على: http://localhost:8000")

    # إنشاء الخادم
    httpd = make_server('localhost', 8000, application)

    try:
        print("الخادم يعمل... اضغط Ctrl+C للإيقاف")
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nتم إيقاف الخادم.")
        save_data()  # حفظ البيانات قبل الإغلاق
