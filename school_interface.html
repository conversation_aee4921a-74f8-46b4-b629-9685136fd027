<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المدرسة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .content {
            padding: 20px;
        }
        .nav-link {
            color: rgba(255,255,255,.75);
            cursor: pointer;
        }
        .nav-link:hover {
            color: white;
        }
        .active {
            background-color: #007bff;
            color: white;
        }
        .page {
            display: none;
        }
        .page.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h3 class="text-light">نظام إدارة المدرسة</h3>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" onclick="showPage('home')">
                                <i class="bi bi-house-door me-2"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" onclick="showPage('students')">
                                <i class="bi bi-mortarboard me-2"></i>
                                الطلاب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" onclick="showPage('teachers')">
                                <i class="bi bi-person me-2"></i>
                                المعلمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" onclick="showPage('classes')">
                                <i class="bi bi-book me-2"></i>
                                الفصول الدراسية
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                
                <!-- الصفحة الرئيسية -->
                <div id="home" class="page active">
                    <div class="container mt-5">
                        <div class="jumbotron bg-light p-5 rounded">
                            <h1 class="display-4">مرحباً بك في نظام إدارة المدرسة</h1>
                            <p class="lead">هذا النظام يساعدك على إدارة الطلاب والمعلمين والفصول الدراسية بكل سهولة.</p>
                            <hr class="my-4">
                            <div class="row mt-5">
                                <div class="col-md-4">
                                    <div class="card text-center mb-4">
                                        <div class="card-body">
                                            <h5 class="card-title"><i class="bi bi-mortarboard fs-1 text-primary"></i></h5>
                                            <h2 class="card-text" id="students-count">0</h2>
                                            <p class="card-text">طالب</p>
                                            <button class="btn btn-primary" onclick="showPage('students')">إدارة الطلاب</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center mb-4">
                                        <div class="card-body">
                                            <h5 class="card-title"><i class="bi bi-person fs-1 text-success"></i></h5>
                                            <h2 class="card-text" id="teachers-count">0</h2>
                                            <p class="card-text">معلم</p>
                                            <button class="btn btn-success" onclick="showPage('teachers')">إدارة المعلمين</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center mb-4">
                                        <div class="card-body">
                                            <h5 class="card-title"><i class="bi bi-book fs-1 text-warning"></i></h5>
                                            <h2 class="card-text" id="classes-count">0</h2>
                                            <p class="card-text">فصل دراسي</p>
                                            <button class="btn btn-warning" onclick="showPage('classes')">إدارة الفصول</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة الطلاب -->
                <div id="students" class="page">
                    <div class="container mt-4">
                        <h2>إدارة الطلاب</h2>
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                إضافة طالب جديد
                            </div>
                            <div class="card-body">
                                <form id="student-form">
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <label for="student-name" class="form-label">اسم الطالب</label>
                                            <input type="text" class="form-control" id="student-name" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="student-grade" class="form-label">الصف</label>
                                            <input type="text" class="form-control" id="student-grade" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="student-age" class="form-label">العمر</label>
                                            <input type="number" class="form-control" id="student-age" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="student-phone" class="form-label">هاتف ولي الأمر</label>
                                            <input type="text" class="form-control" id="student-phone" required>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">إضافة</button>
                                </form>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                قائمة الطلاب
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>الرقم</th>
                                                <th>الاسم</th>
                                                <th>الصف</th>
                                                <th>العمر</th>
                                                <th>هاتف ولي الأمر</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="students-table">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة المعلمين -->
                <div id="teachers" class="page">
                    <div class="container mt-4">
                        <h2>إدارة المعلمين</h2>
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                إضافة معلم جديد
                            </div>
                            <div class="card-body">
                                <form id="teacher-form">
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <label for="teacher-name" class="form-label">اسم المعلم</label>
                                            <input type="text" class="form-control" id="teacher-name" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="teacher-subject" class="form-label">المادة</label>
                                            <input type="text" class="form-control" id="teacher-subject" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="teacher-phone" class="form-label">رقم الهاتف</label>
                                            <input type="text" class="form-control" id="teacher-phone" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="teacher-email" class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" id="teacher-email" required>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-success">إضافة</button>
                                </form>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                قائمة المعلمين
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>الرقم</th>
                                                <th>الاسم</th>
                                                <th>المادة</th>
                                                <th>رقم الهاتف</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="teachers-table">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة الفصول -->
                <div id="classes" class="page">
                    <div class="container mt-4">
                        <h2>إدارة الفصول الدراسية</h2>
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                إضافة فصل جديد
                            </div>
                            <div class="card-body">
                                <form id="class-form">
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <label for="class-name" class="form-label">اسم الفصل</label>
                                            <input type="text" class="form-control" id="class-name" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="class-grade" class="form-label">المرحلة الدراسية</label>
                                            <input type="text" class="form-control" id="class-grade" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="class-teacher" class="form-label">المعلم المسؤول</label>
                                            <select class="form-select" id="class-teacher" required>
                                                <option value="">اختر المعلم</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="class-room" class="form-label">رقم القاعة</label>
                                            <input type="text" class="form-control" id="class-room" required>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-warning">إضافة</button>
                                </form>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                قائمة الفصول الدراسية
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>الرقم</th>
                                                <th>اسم الفصل</th>
                                                <th>المرحلة الدراسية</th>
                                                <th>المعلم المسؤول</th>
                                                <th>رقم القاعة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="classes-table">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let students = JSON.parse(localStorage.getItem('students') || '[]');
        let teachers = JSON.parse(localStorage.getItem('teachers') || '[]');
        let classes = JSON.parse(localStorage.getItem('classes') || '[]');

        // عرض الصفحات
        function showPage(pageId) {
            document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
            document.getElementById(pageId).classList.add('active');
            
            // تحديث القوائم
            if (pageId === 'students') updateStudentsTable();
            if (pageId === 'teachers') updateTeachersTable();
            if (pageId === 'classes') updateClassesTable();
            
            updateCounts();
        }

        // تحديث العدادات
        function updateCounts() {
            document.getElementById('students-count').textContent = students.length;
            document.getElementById('teachers-count').textContent = teachers.length;
            document.getElementById('classes-count').textContent = classes.length;
        }

        // حفظ البيانات
        function saveData() {
            localStorage.setItem('students', JSON.stringify(students));
            localStorage.setItem('teachers', JSON.stringify(teachers));
            localStorage.setItem('classes', JSON.stringify(classes));
        }

        // إضافة طالب
        document.getElementById('student-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const student = {
                id: students.length + 1,
                name: document.getElementById('student-name').value,
                grade: document.getElementById('student-grade').value,
                age: document.getElementById('student-age').value,
                phone: document.getElementById('student-phone').value
            };
            students.push(student);
            saveData();
            updateStudentsTable();
            updateCounts();
            this.reset();
        });

        // إضافة معلم
        document.getElementById('teacher-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const teacher = {
                id: teachers.length + 1,
                name: document.getElementById('teacher-name').value,
                subject: document.getElementById('teacher-subject').value,
                phone: document.getElementById('teacher-phone').value,
                email: document.getElementById('teacher-email').value
            };
            teachers.push(teacher);
            saveData();
            updateTeachersTable();
            updateTeacherOptions();
            updateCounts();
            this.reset();
        });

        // إضافة فصل
        document.getElementById('class-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const classItem = {
                id: classes.length + 1,
                name: document.getElementById('class-name').value,
                grade: document.getElementById('class-grade').value,
                teacher: document.getElementById('class-teacher').value,
                room: document.getElementById('class-room').value
            };
            classes.push(classItem);
            saveData();
            updateClassesTable();
            updateCounts();
            this.reset();
        });

        // تحديث جدول الطلاب
        function updateStudentsTable() {
            const tbody = document.getElementById('students-table');
            tbody.innerHTML = '';
            students.forEach(student => {
                tbody.innerHTML += `
                    <tr>
                        <td>${student.id}</td>
                        <td>${student.name}</td>
                        <td>${student.grade}</td>
                        <td>${student.age}</td>
                        <td>${student.phone}</td>
                        <td><button class="btn btn-sm btn-danger" onclick="deleteStudent(${student.id})">حذف</button></td>
                    </tr>
                `;
            });
        }

        // تحديث جدول المعلمين
        function updateTeachersTable() {
            const tbody = document.getElementById('teachers-table');
            tbody.innerHTML = '';
            teachers.forEach(teacher => {
                tbody.innerHTML += `
                    <tr>
                        <td>${teacher.id}</td>
                        <td>${teacher.name}</td>
                        <td>${teacher.subject}</td>
                        <td>${teacher.phone}</td>
                        <td>${teacher.email}</td>
                        <td><button class="btn btn-sm btn-danger" onclick="deleteTeacher(${teacher.id})">حذف</button></td>
                    </tr>
                `;
            });
        }

        // تحديث جدول الفصول
        function updateClassesTable() {
            const tbody = document.getElementById('classes-table');
            tbody.innerHTML = '';
            classes.forEach(classItem => {
                tbody.innerHTML += `
                    <tr>
                        <td>${classItem.id}</td>
                        <td>${classItem.name}</td>
                        <td>${classItem.grade}</td>
                        <td>${classItem.teacher}</td>
                        <td>${classItem.room}</td>
                        <td><button class="btn btn-sm btn-danger" onclick="deleteClass(${classItem.id})">حذف</button></td>
                    </tr>
                `;
            });
        }

        // تحديث خيارات المعلمين
        function updateTeacherOptions() {
            const select = document.getElementById('class-teacher');
            select.innerHTML = '<option value="">اختر المعلم</option>';
            teachers.forEach(teacher => {
                select.innerHTML += `<option value="${teacher.name}">${teacher.name}</option>`;
            });
        }

        // حذف طالب
        function deleteStudent(id) {
            students = students.filter(s => s.id !== id);
            saveData();
            updateStudentsTable();
            updateCounts();
        }

        // حذف معلم
        function deleteTeacher(id) {
            teachers = teachers.filter(t => t.id !== id);
            saveData();
            updateTeachersTable();
            updateTeacherOptions();
            updateCounts();
        }

        // حذف فصل
        function deleteClass(id) {
            classes = classes.filter(c => c.id !== id);
            saveData();
            updateClassesTable();
            updateCounts();
        }

        // تحميل البيانات عند بدء التطبيق
        updateCounts();
        updateTeacherOptions();
    </script>
</body>
</html>
