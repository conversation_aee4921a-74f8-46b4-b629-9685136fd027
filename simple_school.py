#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from wsgiref.simple_server import make_server
import json
import os
import urllib.parse

# بيانات مؤقتة
STUDENTS = []
TEACHERS = []
CLASSES = []

# تحميل البيانات
def load_data():
    global STUDENTS, TEACHERS, CLASSES
    try:
        if os.path.exists('students.json'):
            with open('students.json', 'r', encoding='utf-8') as f:
                STUDENTS = json.load(f)
        if os.path.exists('teachers.json'):
            with open('teachers.json', 'r', encoding='utf-8') as f:
                TEACHERS = json.load(f)
        if os.path.exists('classes.json'):
            with open('classes.json', 'r', encoding='utf-8') as f:
                CLASSES = json.load(f)
    except Exception as e:
        print(f"Error loading data: {e}")

# حفظ البيانات
def save_data():
    try:
        with open('students.json', 'w', encoding='utf-8') as f:
            json.dump(STUDENTS, f, ensure_ascii=False, indent=2)
        with open('teachers.json', 'w', encoding='utf-8') as f:
            json.dump(TEACHERS, f, ensure_ascii=False, indent=2)
        with open('classes.json', 'w', encoding='utf-8') as f:
            json.dump(CLASSES, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"Error saving data: {e}")

# قالب HTML
HTML_TEMPLATE = """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المدرسة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
        .sidebar {{ min-height: 100vh; background-color: #343a40; color: white; }}
        .nav-link {{ color: rgba(255,255,255,.75); }}
        .nav-link:hover {{ color: white; }}
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-3 sidebar p-3">
                <h3 class="text-center mb-4">نظام إدارة المدرسة</h3>
                <ul class="nav flex-column">
                    <li class="nav-item"><a class="nav-link" href="/">الرئيسية</a></li>
                    <li class="nav-item"><a class="nav-link" href="/students">الطلاب</a></li>
                    <li class="nav-item"><a class="nav-link" href="/teachers">المعلمين</a></li>
                    <li class="nav-item"><a class="nav-link" href="/classes">الفصول</a></li>
                </ul>
            </div>
            <div class="col-md-9 p-4">
                {content}
            </div>
        </div>
    </div>
</body>
</html>"""

def home_page():
    content = f"""
    <h1>مرحباً بك في نظام إدارة المدرسة</h1>
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h2>{len(STUDENTS)}</h2>
                    <p>طالب</p>
                    <a href="/students" class="btn btn-primary">إدارة الطلاب</a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h2>{len(TEACHERS)}</h2>
                    <p>معلم</p>
                    <a href="/teachers" class="btn btn-success">إدارة المعلمين</a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h2>{len(CLASSES)}</h2>
                    <p>فصل</p>
                    <a href="/classes" class="btn btn-warning">إدارة الفصول</a>
                </div>
            </div>
        </div>
    </div>
    """
    return HTML_TEMPLATE.format(content=content)

def students_page():
    content = """
    <h2>إدارة الطلاب</h2>
    <div class="card mb-4">
        <div class="card-header">إضافة طالب جديد</div>
        <div class="card-body">
            <form action="/students" method="get">
                <input type="hidden" name="action" value="add">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="name" placeholder="اسم الطالب" required>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="grade" placeholder="الصف" required>
                    </div>
                    <div class="col-md-3">
                        <input type="number" class="form-control" name="age" placeholder="العمر" required>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary">إضافة</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="card">
        <div class="card-header">قائمة الطلاب</div>
        <div class="card-body">
            <table class="table">
                <thead>
                    <tr><th>الاسم</th><th>الصف</th><th>العمر</th></tr>
                </thead>
                <tbody>
    """
    
    for student in STUDENTS:
        content += f"<tr><td>{student.get('name', '')}</td><td>{student.get('grade', '')}</td><td>{student.get('age', '')}</td></tr>"
    
    content += """
                </tbody>
            </table>
        </div>
    </div>
    """
    return HTML_TEMPLATE.format(content=content)

def parse_query_string(query_string):
    params = {}
    if query_string:
        for param in query_string.split('&'):
            if '=' in param:
                key, value = param.split('=', 1)
                params[urllib.parse.unquote_plus(key)] = urllib.parse.unquote_plus(value)
    return params

def application(environ, start_response):
    try:
        path = environ.get('PATH_INFO', '/')
        query_string = environ.get('QUERY_STRING', '')
        query_params = parse_query_string(query_string)
        
        # معالجة إضافة طالب
        if path == '/students' and query_params.get('action') == 'add':
            new_student = {
                'name': query_params.get('name', ''),
                'grade': query_params.get('grade', ''),
                'age': query_params.get('age', '')
            }
            STUDENTS.append(new_student)
            save_data()
        
        # توجيه الصفحات
        if path == '/':
            response_body = home_page()
        elif path == '/students':
            response_body = students_page()
        else:
            response_body = HTML_TEMPLATE.format(content="<h1>الصفحة غير موجودة</h1>")
        
        response_body = response_body.encode('utf-8')
        status = '200 OK'
        headers = [('Content-Type', 'text/html; charset=utf-8')]
        
    except Exception as e:
        print(f"Error: {e}")
        error_html = HTML_TEMPLATE.format(content=f"<h1>خطأ: {str(e)}</h1>")
        response_body = error_html.encode('utf-8')
        status = '500 Internal Server Error'
        headers = [('Content-Type', 'text/html; charset=utf-8')]
    
    start_response(status, headers)
    return [response_body]

if __name__ == '__main__':
    load_data()
    print("Starting school management system...")
    print("Access at: http://localhost:8000")
    
    httpd = make_server('localhost', 8000, application)
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("Server stopped.")
        save_data()
