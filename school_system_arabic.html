<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المدرسة - النسخة العربية الكاملة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        .content {
            padding: 20px;
        }
        .nav-link {
            color: rgba(255,255,255,.85);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(-5px);
        }
        .page {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }
        .page.active {
            display: block;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card.students {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.teachers {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-card.classes {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .stat-card.attendance {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        .btn {
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .attendance-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .present { background-color: #d4edda; color: #155724; }
        .absent { background-color: #f8d7da; color: #721c24; }
        .late { background-color: #fff3cd; color: #856404; }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .arabic-title {
            font-weight: 700;
            color: #2c3e50;
        }
        .notification {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            min-width: 300px;
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        .form-label {
            font-weight: 600;
            color: #495057;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .modal-title {
            font-weight: 700;
        }
        .badge {
            font-size: 0.75em;
            font-weight: 600;
        }
        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            text-align: center;
            padding: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 1rem;
        }
        .nav-section {
            margin-bottom: 1rem;
        }
        .nav-section-title {
            font-size: 0.8rem;
            font-weight: 600;
            color: rgba(255,255,255,0.6);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
            padding: 0 1rem;
        }
        .welcome-message {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .feature-highlight {
            border-right: 4px solid #667eea;
            background: #f8f9fa;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 10px 10px 0;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 5px;
        }
        .status-active { background-color: #28a745; }
        .status-inactive { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="position-sticky pt-3">
                    <div class="sidebar-brand">
                        <i class="bi bi-mortarboard-fill fs-1 mb-2 d-block"></i>
                        نظام إدارة المدرسة
                        <small class="d-block mt-1 opacity-75">الإصدار العربي الكامل</small>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">الإدارة الرئيسية</div>
                        <ul class="nav flex-column px-3">
                            <li class="nav-item">
                                <a class="nav-link py-3 px-4 active" onclick="showPage('dashboard')">
                                    <i class="bi bi-speedometer2 me-2"></i>
                                    لوحة التحكم
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link py-3 px-4" onclick="showPage('students')">
                                    <i class="bi bi-mortarboard me-2"></i>
                                    إدارة الطلاب
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link py-3 px-4" onclick="showPage('teachers')">
                                    <i class="bi bi-person-badge me-2"></i>
                                    إدارة المعلمين
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link py-3 px-4" onclick="showPage('classes')">
                                    <i class="bi bi-book me-2"></i>
                                    إدارة الفصول
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">المتابعة والتقييم</div>
                        <ul class="nav flex-column px-3">
                            <li class="nav-item">
                                <a class="nav-link py-3 px-4" onclick="showPage('attendance')">
                                    <i class="bi bi-calendar-check me-2"></i>
                                    نظام الحضور والغياب
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link py-3 px-4" onclick="showPage('reports')">
                                    <i class="bi bi-graph-up me-2"></i>
                                    التقارير والإحصائيات
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">النظام</div>
                        <ul class="nav flex-column px-3">
                            <li class="nav-item">
                                <a class="nav-link py-3 px-4" onclick="showPage('settings')">
                                    <i class="bi bi-gear me-2"></i>
                                    الإعدادات
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                
                <!-- لوحة التحكم -->
                <div id="dashboard" class="page active">
                    <div class="welcome-message">
                        <h1 class="arabic-title mb-3">مرحباً بك في نظام إدارة المدرسة</h1>
                        <p class="lead mb-0">نظام شامل ومتطور لإدارة جميع شؤون المدرسة بكفاءة عالية</p>
                        <small class="d-block mt-2 opacity-75">تاريخ اليوم: <span id="current-date"></span></small>
                    </div>

                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                        <h2 class="arabic-title">نظرة عامة سريعة</h2>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData()">
                                    <i class="bi bi-download me-1"></i>تصدير البيانات
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printReport()">
                                    <i class="bi bi-printer me-1"></i>طباعة التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- الإحصائيات السريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card students">
                                <div class="card-body text-center">
                                    <i class="bi bi-mortarboard fs-1 mb-2"></i>
                                    <h2 class="card-text" id="dashboard-students-count">0</h2>
                                    <p class="card-text">إجمالي الطلاب</p>
                                    <small class="opacity-75">مسجل في النظام</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card teachers">
                                <div class="card-body text-center">
                                    <i class="bi bi-person-badge fs-1 mb-2"></i>
                                    <h2 class="card-text" id="dashboard-teachers-count">0</h2>
                                    <p class="card-text">إجمالي المعلمين</p>
                                    <small class="opacity-75">عضو هيئة تدريس</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card classes">
                                <div class="card-body text-center">
                                    <i class="bi bi-book fs-1 mb-2"></i>
                                    <h2 class="card-text" id="dashboard-classes-count">0</h2>
                                    <p class="card-text">إجمالي الفصول</p>
                                    <small class="opacity-75">فصل دراسي نشط</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card attendance">
                                <div class="card-body text-center">
                                    <i class="bi bi-calendar-check fs-1 mb-2"></i>
                                    <h2 class="card-text" id="dashboard-attendance-rate">0%</h2>
                                    <p class="card-text">معدل الحضور اليوم</p>
                                    <small class="opacity-75" id="attendance-date"></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الميزات السريعة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-bar-chart me-2"></i>
                                        توزيع الطلاب حسب الصف
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="studentsChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-pie-chart me-2"></i>
                                        معدل الحضور الأسبوعي
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="attendanceChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإجراءات السريعة -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="feature-highlight">
                                <h6><i class="bi bi-plus-circle me-2 text-primary"></i>إضافة سريعة</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                                        إضافة طالب جديد
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                                        إضافة معلم جديد
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" data-bs-toggle="modal" data-bs-target="#addClassModal">
                                        إضافة فصل جديد
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-highlight">
                                <h6><i class="bi bi-calendar-check me-2 text-success"></i>الحضور اليوم</h6>
                                <p class="mb-2">تسجيل سريع للحضور</p>
                                <button class="btn btn-success btn-sm w-100" onclick="showPage('attendance')">
                                    انتقال لنظام الحضور
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-highlight">
                                <h6><i class="bi bi-graph-up me-2 text-info"></i>التقارير</h6>
                                <p class="mb-2">عرض التقارير والإحصائيات</p>
                                <button class="btn btn-info btn-sm w-100" onclick="showPage('reports')">
                                    عرض التقارير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة إدارة الطلاب -->
                <div id="students" class="page">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2 arabic-title">إدارة الطلاب</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                                <i class="bi bi-plus-circle me-1"></i>إضافة طالب جديد
                            </button>
                        </div>
                    </div>

                    <!-- شريط البحث والفلترة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                <input type="text" class="form-control search-box" id="student-search" placeholder="البحث عن طالب بالاسم أو الرقم...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="student-grade-filter">
                                <option value="">جميع الصفوف الدراسية</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="student-class-filter">
                                <option value="">جميع الفصول الدراسية</option>
                            </select>
                        </div>
                    </div>

                    <!-- جدول الطلاب -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-list me-2"></i>
                                قائمة الطلاب المسجلين
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرقم التسلسلي</th>
                                            <th>الاسم الكامل</th>
                                            <th>الصف الدراسي</th>
                                            <th>الفصل</th>
                                            <th>العمر</th>
                                            <th>هاتف ولي الأمر</th>
                                            <th>معدل الحضور</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="students-table">
                                        <tr>
                                            <td colspan="9" class="text-center text-muted py-4">
                                                <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                                لا توجد بيانات طلاب حالياً
                                                <br>
                                                <small>يمكنك إضافة طلاب جدد باستخدام الزر أعلاه</small>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->
    <!-- نافذة إضافة طالب -->
    <div class="modal fade" id="addStudentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة طالب جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="student-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="student-name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="student-name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-id" class="form-label">الرقم التسلسلي *</label>
                                <input type="text" class="form-control" id="student-id" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-grade" class="form-label">الصف الدراسي *</label>
                                <select class="form-select" id="student-grade" required>
                                    <option value="">اختر الصف</option>
                                    <option value="الأول الابتدائي">الأول الابتدائي</option>
                                    <option value="الثاني الابتدائي">الثاني الابتدائي</option>
                                    <option value="الثالث الابتدائي">الثالث الابتدائي</option>
                                    <option value="الرابع الابتدائي">الرابع الابتدائي</option>
                                    <option value="الخامس الابتدائي">الخامس الابتدائي</option>
                                    <option value="السادس الابتدائي">السادس الابتدائي</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-class" class="form-label">الفصل *</label>
                                <select class="form-select" id="student-class" required>
                                    <option value="">اختر الفصل</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-age" class="form-label">العمر *</label>
                                <input type="number" class="form-control" id="student-age" min="5" max="18" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-gender" class="form-label">الجنس *</label>
                                <select class="form-select" id="student-gender" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="ذكر">ذكر</option>
                                    <option value="أنثى">أنثى</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-phone" class="form-label">هاتف ولي الأمر *</label>
                                <input type="tel" class="form-control" id="student-phone" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="student-email">
                            </div>
                            <div class="col-12 mb-3">
                                <label for="student-address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="student-address" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="addStudent()">إضافة الطالب</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script>
        // البيانات المحلية
        let students = JSON.parse(localStorage.getItem('students_arabic') || '[]');
        let teachers = JSON.parse(localStorage.getItem('teachers_arabic') || '[]');
        let classes = JSON.parse(localStorage.getItem('classes_arabic') || '[]');
        let attendance = JSON.parse(localStorage.getItem('attendance_arabic') || '{}');
        let settings = JSON.parse(localStorage.getItem('settings_arabic') || '{"schoolName": "مدرسة المستقبل الابتدائية", "academicYear": "2024-2025", "attendanceTime": "07:30", "lateThreshold": 15}');

        // متغيرات الرسوم البيانية
        let studentsChart, attendanceChart;

        // عرض الصفحات
        function showPage(pageId) {
            // إخفاء جميع الصفحات
            document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));

            // إظهار الصفحة المطلوبة
            document.getElementById(pageId).classList.add('active');
            event.target.classList.add('active');

            // تحديث المحتوى حسب الصفحة
            switch(pageId) {
                case 'dashboard':
                    updateDashboard();
                    break;
                case 'students':
                    updateStudentsTable();
                    updateFilters();
                    break;
                case 'teachers':
                    updateTeachersTable();
                    break;
                case 'classes':
                    updateClassesTable();
                    break;
            }
        }

        // حفظ البيانات
        function saveData() {
            localStorage.setItem('students_arabic', JSON.stringify(students));
            localStorage.setItem('teachers_arabic', JSON.stringify(teachers));
            localStorage.setItem('classes_arabic', JSON.stringify(classes));
            localStorage.setItem('attendance_arabic', JSON.stringify(attendance));
            localStorage.setItem('settings_arabic', JSON.stringify(settings));
        }

        // تحديث لوحة التحكم
        function updateDashboard() {
            // تحديث العدادات
            document.getElementById('dashboard-students-count').textContent = students.length;
            document.getElementById('dashboard-teachers-count').textContent = teachers.length;
            document.getElementById('dashboard-classes-count').textContent = classes.length;

            // حساب معدل الحضور اليوم
            const today = new Date().toISOString().split('T')[0];
            const todayAttendance = attendance[today] || {};
            const totalStudents = students.length;
            const presentStudents = Object.values(todayAttendance).filter(status => status === 'حاضر' || status === 'متأخر').length;
            const attendanceRate = totalStudents > 0 ? Math.round((presentStudents / totalStudents) * 100) : 0;

            document.getElementById('dashboard-attendance-rate').textContent = attendanceRate + '%';
            document.getElementById('attendance-date').textContent = new Date().toLocaleDateString('ar-SA');

            // تحديث التاريخ الحالي
            document.getElementById('current-date').textContent = new Date().toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            // تحديث الرسوم البيانية
            updateDashboardCharts();
        }

        // تحديث الرسوم البيانية في لوحة التحكم
        function updateDashboardCharts() {
            // رسم بياني لتوزيع الطلاب حسب الصف
            const gradeData = {};
            students.forEach(student => {
                gradeData[student.grade] = (gradeData[student.grade] || 0) + 1;
            });

            const ctx1 = document.getElementById('studentsChart');
            if (studentsChart) studentsChart.destroy();
            studentsChart = new Chart(ctx1, {
                type: 'bar',
                data: {
                    labels: Object.keys(gradeData),
                    datasets: [{
                        label: 'عدد الطلاب',
                        data: Object.values(gradeData),
                        backgroundColor: [
                            'rgba(102, 126, 234, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(255, 205, 86, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ],
                        borderColor: [
                            'rgba(102, 126, 234, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(255, 205, 86, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: 'توزيع الطلاب حسب الصفوف الدراسية',
                            font: {
                                family: 'Tajawal',
                                size: 14,
                                weight: 'bold'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1,
                                font: {
                                    family: 'Tajawal'
                                }
                            },
                            title: {
                                display: true,
                                text: 'عدد الطلاب',
                                font: {
                                    family: 'Tajawal'
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    family: 'Tajawal'
                                }
                            },
                            title: {
                                display: true,
                                text: 'الصفوف الدراسية',
                                font: {
                                    family: 'Tajawal'
                                }
                            }
                        }
                    }
                }
            });

            // رسم بياني لمعدل الحضور الأسبوعي
            const weeklyAttendance = getWeeklyAttendanceData();
            const ctx2 = document.getElementById('attendanceChart');
            if (attendanceChart) attendanceChart.destroy();
            attendanceChart = new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: weeklyAttendance.labels,
                    datasets: [{
                        label: 'نسبة الحضور %',
                        data: weeklyAttendance.data,
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: 'rgba(75, 192, 192, 1)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: 'معدل الحضور خلال الأسبوع الماضي',
                            font: {
                                family: 'Tajawal',
                                size: 14,
                                weight: 'bold'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                },
                                font: {
                                    family: 'Tajawal'
                                }
                            },
                            title: {
                                display: true,
                                text: 'نسبة الحضور (%)',
                                font: {
                                    family: 'Tajawal'
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    family: 'Tajawal'
                                }
                            },
                            title: {
                                display: true,
                                text: 'أيام الأسبوع',
                                font: {
                                    family: 'Tajawal'
                                }
                            }
                        }
                    }
                }
            });
        }

        // الحصول على بيانات الحضور الأسبوعي
        function getWeeklyAttendanceData() {
            const labels = [];
            const data = [];
            const today = new Date();

            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];
                const dayName = date.toLocaleDateString('ar-SA', { weekday: 'short' });

                labels.push(dayName);

                const dayAttendance = attendance[dateStr] || {};
                const totalStudents = students.length;
                const presentStudents = Object.values(dayAttendance).filter(status => status === 'حاضر' || status === 'متأخر').length;
                const rate = totalStudents > 0 ? Math.round((presentStudents / totalStudents) * 100) : 0;

                data.push(rate);
            }

            return { labels, data };
        }

        // إضافة طالب جديد
        function addStudent() {
            const student = {
                id: Date.now(),
                studentId: document.getElementById('student-id').value,
                name: document.getElementById('student-name').value,
                grade: document.getElementById('student-grade').value,
                class: document.getElementById('student-class').value,
                age: parseInt(document.getElementById('student-age').value),
                gender: document.getElementById('student-gender').value,
                phone: document.getElementById('student-phone').value,
                email: document.getElementById('student-email').value,
                address: document.getElementById('student-address').value,
                attendanceRate: 0,
                status: 'نشط',
                createdAt: new Date().toISOString()
            };

            // التحقق من عدم تكرار الرقم التسلسلي
            if (students.some(s => s.studentId === student.studentId)) {
                showNotification('الرقم التسلسلي موجود مسبقاً!', 'خطأ');
                return;
            }

            students.push(student);
            saveData();
            updateStudentsTable();
            updateDashboard();

            // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
            bootstrap.Modal.getInstance(document.getElementById('addStudentModal')).hide();
            document.getElementById('student-form').reset();

            showNotification('تم إضافة الطالب بنجاح!', 'نجح');
        }

        // تحديث جدول الطلاب
        function updateStudentsTable() {
            const tbody = document.getElementById('students-table');

            if (students.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted py-4">
                            <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                            لا توجد بيانات طلاب حالياً
                            <br>
                            <small>يمكنك إضافة طلاب جدد باستخدام الزر أعلاه</small>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            let filteredStudents = students;

            // تطبيق الفلاتر
            const searchTerm = document.getElementById('student-search')?.value.toLowerCase() || '';
            const gradeFilter = document.getElementById('student-grade-filter')?.value || '';
            const classFilter = document.getElementById('student-class-filter')?.value || '';

            if (searchTerm) {
                filteredStudents = filteredStudents.filter(student =>
                    student.name.toLowerCase().includes(searchTerm) ||
                    student.studentId.toLowerCase().includes(searchTerm)
                );
            }

            if (gradeFilter) {
                filteredStudents = filteredStudents.filter(student => student.grade === gradeFilter);
            }

            if (classFilter) {
                filteredStudents = filteredStudents.filter(student => student.class === classFilter);
            }

            filteredStudents.forEach(student => {
                const attendanceRate = calculateStudentAttendanceRate(student.id);
                const statusBadge = student.status === 'نشط' ? 'bg-success' : 'bg-secondary';
                tbody.innerHTML += `
                    <tr>
                        <td><strong>${student.studentId}</strong></td>
                        <td>${student.name}</td>
                        <td>${student.grade}</td>
                        <td>${student.class || 'غير محدد'}</td>
                        <td>${student.age} سنة</td>
                        <td>${student.phone}</td>
                        <td>
                            <span class="badge ${attendanceRate >= 90 ? 'bg-success' : attendanceRate >= 75 ? 'bg-warning' : 'bg-danger'}">
                                ${attendanceRate}%
                            </span>
                        </td>
                        <td>
                            <span class="badge ${statusBadge}">${student.status}</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editStudent(${student.id})" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-info" onclick="viewStudentDetails(${student.id})" title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteStudent(${student.id})" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }

        // حساب معدل حضور الطالب
        function calculateStudentAttendanceRate(studentId) {
            let totalDays = 0;
            let presentDays = 0;

            Object.keys(attendance).forEach(date => {
                if (attendance[date][studentId]) {
                    totalDays++;
                    if (attendance[date][studentId] === 'حاضر' || attendance[date][studentId] === 'متأخر') {
                        presentDays++;
                    }
                }
            });

            return totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;
        }

        // تحديث الفلاتر
        function updateFilters() {
            // فلتر الصفوف للطلاب
            const gradeFilter = document.getElementById('student-grade-filter');
            if (gradeFilter) {
                const grades = [...new Set(students.map(s => s.grade))];
                gradeFilter.innerHTML = '<option value="">جميع الصفوف الدراسية</option>';
                grades.forEach(grade => {
                    gradeFilter.innerHTML += `<option value="${grade}">${grade}</option>`;
                });
            }

            // فلتر الفصول للطلاب
            const classFilter = document.getElementById('student-class-filter');
            if (classFilter) {
                const classNames = [...new Set(classes.map(c => c.name))];
                classFilter.innerHTML = '<option value="">جميع الفصول الدراسية</option>';
                classNames.forEach(className => {
                    classFilter.innerHTML += `<option value="${className}">${className}</option>`;
                });
            }
        }

        // حذف طالب
        function deleteStudent(id) {
            if (confirm('هل أنت متأكد من حذف هذا الطالب؟\nسيتم حذف جميع بيانات الحضور المرتبطة به.')) {
                students = students.filter(s => s.id !== id);

                // حذف بيانات الحضور المرتبطة
                Object.keys(attendance).forEach(date => {
                    delete attendance[date][id];
                    delete attendance[date][id + '_time'];
                    delete attendance[date][id + '_note'];
                });

                saveData();
                updateStudentsTable();
                updateDashboard();
                showNotification('تم حذف الطالب بنجاح!', 'نجح');
            }
        }

        // عرض الإشعارات
        function showNotification(message, type = 'معلومات') {
            // تحديد نوع الإشعار
            let alertType = 'info';
            let icon = 'bi-info-circle';

            switch(type) {
                case 'نجح':
                    alertType = 'success';
                    icon = 'bi-check-circle';
                    break;
                case 'خطأ':
                    alertType = 'danger';
                    icon = 'bi-exclamation-triangle';
                    break;
                case 'تحذير':
                    alertType = 'warning';
                    icon = 'bi-exclamation-circle';
                    break;
            }

            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.className = `alert alert-${alertType} alert-dismissible fade show notification`;
            notification.innerHTML = `
                <i class="bi ${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // إزالة الإشعار تلقائياً بعد 4 ثوان
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 4000);
        }

        // إضافة مستمعي الأحداث للبحث والفلترة
        function addEventListeners() {
            // البحث في الطلاب
            const studentSearch = document.getElementById('student-search');
            if (studentSearch) {
                studentSearch.addEventListener('input', updateStudentsTable);
            }

            // فلاتر الطلاب
            const studentFilters = document.querySelectorAll('#student-grade-filter, #student-class-filter');
            studentFilters.forEach(filter => {
                filter.addEventListener('change', updateStudentsTable);
            });
        }

        // التهيئة الأولية
        function initializeApp() {
            // تحديث لوحة التحكم
            updateDashboard();

            // تحديث الفلاتر
            updateFilters();

            // إضافة مستمعي الأحداث
            addEventListeners();

            // إضافة بيانات تجريبية إذا لم تكن موجودة
            if (students.length === 0 && teachers.length === 0 && classes.length === 0) {
                generateSampleData();
            }
        }

        // إنشاء بيانات تجريبية
        function generateSampleData() {
            // إضافة معلمين تجريبيين
            const sampleTeachers = [
                { id: Date.now() + 1, teacherId: 'T001', name: 'أحمد محمد علي', subject: 'اللغة العربية', phone: '0501234567', email: '<EMAIL>', qualification: 'بكالوريوس', experience: 5, status: 'نشط' },
                { id: Date.now() + 2, teacherId: 'T002', name: 'فاطمة سالم أحمد', subject: 'الرياضيات', phone: '0507654321', email: '<EMAIL>', qualification: 'ماجستير', experience: 8, status: 'نشط' },
                { id: Date.now() + 3, teacherId: 'T003', name: 'محمد عبدالله سعد', subject: 'العلوم', phone: '0509876543', email: '<EMAIL>', qualification: 'بكالوريوس', experience: 3, status: 'نشط' }
            ];

            // إضافة فصول تجريبية
            const sampleClasses = [
                { id: Date.now() + 1, name: '1أ', grade: 'الأول الابتدائي', teacher: 'أحمد محمد علي', room: '101', capacity: 25 },
                { id: Date.now() + 2, name: '2ب', grade: 'الثاني الابتدائي', teacher: 'فاطمة سالم أحمد', room: '102', capacity: 30 },
                { id: Date.now() + 3, name: '3ج', grade: 'الثالث الابتدائي', teacher: 'محمد عبدالله سعد', room: '103', capacity: 28 }
            ];

            // إضافة طلاب تجريبيين
            const sampleStudents = [
                { id: Date.now() + 1, studentId: 'S001', name: 'علي أحمد محمد', grade: 'الأول الابتدائي', class: '1أ', age: 7, gender: 'ذكر', phone: '0551234567', email: '', address: 'الرياض', status: 'نشط' },
                { id: Date.now() + 2, studentId: 'S002', name: 'نورا سعد علي', grade: 'الأول الابتدائي', class: '1أ', age: 6, gender: 'أنثى', phone: '0557654321', email: '', address: 'الرياض', status: 'نشط' },
                { id: Date.now() + 3, studentId: 'S003', name: 'خالد محمد سالم', grade: 'الثاني الابتدائي', class: '2ب', age: 8, gender: 'ذكر', phone: '0559876543', email: '', address: 'الرياض', status: 'نشط' },
                { id: Date.now() + 4, studentId: 'S004', name: 'مريم عبدالله أحمد', grade: 'الثاني الابتدائي', class: '2ب', age: 7, gender: 'أنثى', phone: '0552468135', email: '', address: 'الرياض', status: 'نشط' },
                { id: Date.now() + 5, studentId: 'S005', name: 'يوسف سالم محمد', grade: 'الثالث الابتدائي', class: '3ج', age: 9, gender: 'ذكر', phone: '0558642097', email: '', address: 'الرياض', status: 'نشط' }
            ];

            // دمج البيانات التجريبية مع البيانات الحالية
            teachers.push(...sampleTeachers);
            classes.push(...sampleClasses);
            students.push(...sampleStudents);

            // إنشاء بيانات حضور تجريبية للأسبوع الماضي
            const today = new Date();
            for (let i = 7; i >= 1; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];

                if (!attendance[dateStr]) attendance[dateStr] = {};

                sampleStudents.forEach(student => {
                    const random = Math.random();
                    if (random > 0.1) { // 90% حضور
                        attendance[dateStr][student.id] = random > 0.05 ? 'حاضر' : 'متأخر';
                        attendance[dateStr][student.id + '_time'] = random > 0.05 ? '07:30' : '07:45';
                    } else {
                        attendance[dateStr][student.id] = 'غائب';
                    }
                });
            }

            saveData();
            showNotification('تم إنشاء البيانات التجريبية بنجاح! يمكنك الآن استكشاف النظام.', 'نجح');
        }

        // وظائف مؤقتة (قيد التطوير)
        function updateTeachersTable() { showNotification('صفحة المعلمين قيد التطوير', 'معلومات'); }
        function updateClassesTable() { showNotification('صفحة الفصول قيد التطوير', 'معلومات'); }
        function editStudent(id) { showNotification('ميزة التعديل قيد التطوير', 'معلومات'); }
        function viewStudentDetails(id) { showNotification('ميزة عرض التفاصيل قيد التطوير', 'معلومات'); }
        function generateTeacherReport() { showNotification('تقرير المعلمين قيد التطوير', 'معلومات'); }
        function exportData() { showNotification('ميزة التصدير قيد التطوير', 'معلومات'); }
        function printReport() { showNotification('ميزة الطباعة قيد التطوير', 'معلومات'); }

        // تشغيل التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            showNotification('مرحباً بك في نظام إدارة المدرسة العربي الكامل!', 'نجح');
        });
    </script>
</body>
</html>
