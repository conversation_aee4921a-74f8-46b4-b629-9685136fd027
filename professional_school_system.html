<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المدرسة الاحترافي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        .content {
            padding: 20px;
        }
        .nav-link {
            color: rgba(255,255,255,.85);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(-5px);
        }
        .page {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }
        .page.active {
            display: block;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card.students {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.teachers {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-card.classes {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .stat-card.attendance {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        .btn {
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .attendance-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .present { background-color: #d4edda; color: #155724; }
        .absent { background-color: #f8d7da; color: #721c24; }
        .late { background-color: #fff3cd; color: #856404; }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <i class="bi bi-mortarboard-fill fs-1 mb-2"></i>
                        <h4 class="text-light">نظام إدارة المدرسة</h4>
                        <small class="text-light opacity-75">الإصدار الاحترافي</small>
                    </div>
                    <ul class="nav flex-column px-3">
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4 active" onclick="showPage('dashboard')">
                                <i class="bi bi-speedometer2 me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" onclick="showPage('students')">
                                <i class="bi bi-mortarboard me-2"></i>
                                إدارة الطلاب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" onclick="showPage('teachers')">
                                <i class="bi bi-person-badge me-2"></i>
                                إدارة المعلمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" onclick="showPage('classes')">
                                <i class="bi bi-book me-2"></i>
                                إدارة الفصول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" onclick="showPage('attendance')">
                                <i class="bi bi-calendar-check me-2"></i>
                                نظام الغياب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" onclick="showPage('reports')">
                                <i class="bi bi-graph-up me-2"></i>
                                التقارير والإحصائيات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-3 px-4" onclick="showPage('settings')">
                                <i class="bi bi-gear me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                
                <!-- لوحة التحكم -->
                <div id="dashboard" class="page active">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">لوحة التحكم</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData()">
                                    <i class="bi bi-download me-1"></i>تصدير البيانات
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printReport()">
                                    <i class="bi bi-printer me-1"></i>طباعة التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- الإحصائيات السريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card students">
                                <div class="card-body text-center">
                                    <i class="bi bi-mortarboard fs-1 mb-2"></i>
                                    <h2 class="card-text" id="dashboard-students-count">0</h2>
                                    <p class="card-text">إجمالي الطلاب</p>
                                    <small class="opacity-75">مسجل في النظام</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card teachers">
                                <div class="card-body text-center">
                                    <i class="bi bi-person-badge fs-1 mb-2"></i>
                                    <h2 class="card-text" id="dashboard-teachers-count">0</h2>
                                    <p class="card-text">إجمالي المعلمين</p>
                                    <small class="opacity-75">عضو هيئة تدريس</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card classes">
                                <div class="card-body text-center">
                                    <i class="bi bi-book fs-1 mb-2"></i>
                                    <h2 class="card-text" id="dashboard-classes-count">0</h2>
                                    <p class="card-text">إجمالي الفصول</p>
                                    <small class="opacity-75">فصل دراسي نشط</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card attendance">
                                <div class="card-body text-center">
                                    <i class="bi bi-calendar-check fs-1 mb-2"></i>
                                    <h2 class="card-text" id="dashboard-attendance-rate">0%</h2>
                                    <p class="card-text">معدل الحضور اليوم</p>
                                    <small class="opacity-75" id="attendance-date"></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الرسوم البيانية -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-bar-chart me-2"></i>
                                        توزيع الطلاب حسب الصف
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="studentsChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-pie-chart me-2"></i>
                                        معدل الحضور الأسبوعي
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="attendanceChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأنشطة الأخيرة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-clock-history me-2"></i>
                                        الأنشطة الأخيرة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div id="recent-activities">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="bg-primary rounded-circle p-2 me-3">
                                                <i class="bi bi-person-plus text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">تم إضافة طالب جديد</h6>
                                                <small class="text-muted">منذ دقائق قليلة</small>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="bg-success rounded-circle p-2 me-3">
                                                <i class="bi bi-check-circle text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">تم تسجيل الحضور للفصل الأول</h6>
                                                <small class="text-muted">منذ 15 دقيقة</small>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-info rounded-circle p-2 me-3">
                                                <i class="bi bi-file-earmark-text text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">تم إنشاء تقرير شهري</h6>
                                                <small class="text-muted">منذ ساعة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة إدارة الطلاب -->
                <div id="students" class="page">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">إدارة الطلاب</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                                <i class="bi bi-plus-circle me-1"></i>إضافة طالب جديد
                            </button>
                        </div>
                    </div>

                    <!-- شريط البحث والفلترة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                <input type="text" class="form-control search-box" id="student-search" placeholder="البحث عن طالب...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="student-grade-filter">
                                <option value="">جميع الصفوف</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="student-class-filter">
                                <option value="">جميع الفصول</option>
                            </select>
                        </div>
                    </div>

                    <!-- جدول الطلاب -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرقم التسلسلي</th>
                                            <th>الاسم الكامل</th>
                                            <th>الصف</th>
                                            <th>الفصل</th>
                                            <th>العمر</th>
                                            <th>هاتف ولي الأمر</th>
                                            <th>معدل الحضور</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="students-table">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة إدارة المعلمين -->
                <div id="teachers" class="page">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">إدارة المعلمين</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                                <i class="bi bi-plus-circle me-1"></i>إضافة معلم جديد
                            </button>
                        </div>
                    </div>

                    <!-- شريط البحث -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                <input type="text" class="form-control search-box" id="teacher-search" placeholder="البحث عن معلم...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="teacher-subject-filter">
                                <option value="">جميع المواد</option>
                            </select>
                        </div>
                    </div>

                    <!-- جدول المعلمين -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرقم</th>
                                            <th>الاسم الكامل</th>
                                            <th>المادة</th>
                                            <th>رقم الهاتف</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الفصول المسؤول عنها</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="teachers-table">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة إدارة الفصول -->
                <div id="classes" class="page">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">إدارة الفصول الدراسية</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#addClassModal">
                                <i class="bi bi-plus-circle me-1"></i>إضافة فصل جديد
                            </button>
                        </div>
                    </div>

                    <!-- جدول الفصول -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرقم</th>
                                            <th>اسم الفصل</th>
                                            <th>المرحلة الدراسية</th>
                                            <th>المعلم المسؤول</th>
                                            <th>رقم القاعة</th>
                                            <th>عدد الطلاب</th>
                                            <th>معدل الحضور</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="classes-table">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة التقارير -->
                <div id="reports" class="page">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">التقارير والإحصائيات</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-outline-secondary" onclick="generateReport('daily')">
                                    <i class="bi bi-calendar-day me-1"></i>تقرير يومي
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="generateReport('weekly')">
                                    <i class="bi bi-calendar-week me-1"></i>تقرير أسبوعي
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="generateReport('monthly')">
                                    <i class="bi bi-calendar-month me-1"></i>تقرير شهري
                                </button>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="exportReportToPDF()">
                                <i class="bi bi-file-earmark-pdf me-1"></i>تصدير PDF
                            </button>
                        </div>
                    </div>

                    <!-- فلاتر التقارير -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label for="report-start-date" class="form-label">من تاريخ:</label>
                            <input type="date" class="form-control" id="report-start-date">
                        </div>
                        <div class="col-md-3">
                            <label for="report-end-date" class="form-label">إلى تاريخ:</label>
                            <input type="date" class="form-control" id="report-end-date">
                        </div>
                        <div class="col-md-3">
                            <label for="report-class-filter" class="form-label">الفصل:</label>
                            <select class="form-select" id="report-class-filter">
                                <option value="">جميع الفصول</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-primary d-block w-100" onclick="generateCustomReport()">
                                <i class="bi bi-file-earmark-bar-graph me-1"></i>إنشاء التقرير
                            </button>
                        </div>
                    </div>

                    <!-- الإحصائيات السريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card stat-card students">
                                <div class="card-body text-center">
                                    <i class="bi bi-calendar-check fs-1 mb-2"></i>
                                    <h3 id="total-attendance-days">0</h3>
                                    <p>أيام الحضور المسجلة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card teachers">
                                <div class="card-body text-center">
                                    <i class="bi bi-percent fs-1 mb-2"></i>
                                    <h3 id="overall-attendance-rate">0%</h3>
                                    <p>معدل الحضور العام</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card classes">
                                <div class="card-body text-center">
                                    <i class="bi bi-trophy fs-1 mb-2"></i>
                                    <h3 id="best-class-attendance">0%</h3>
                                    <p>أفضل فصل في الحضور</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card attendance">
                                <div class="card-body text-center">
                                    <i class="bi bi-exclamation-triangle fs-1 mb-2"></i>
                                    <h3 id="students-at-risk">0</h3>
                                    <p>طلاب في خطر (أقل من 75%)</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الرسوم البيانية -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-graph-up me-2"></i>
                                        تقرير الحضور التفصيلي
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="reportChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-pie-chart me-2"></i>
                                        توزيع حالات الحضور
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="attendanceDistributionChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جداول التقارير -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-award me-2"></i>
                                        أفضل 10 طلاب في الحضور
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>الترتيب</th>
                                                    <th>اسم الطالب</th>
                                                    <th>الفصل</th>
                                                    <th>معدل الحضور</th>
                                                </tr>
                                            </thead>
                                            <tbody id="top-students-table">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-exclamation-circle me-2"></i>
                                        الطلاب الذين يحتاجون متابعة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>اسم الطالب</th>
                                                    <th>الفصل</th>
                                                    <th>معدل الحضور</th>
                                                    <th>أيام الغياب</th>
                                                </tr>
                                            </thead>
                                            <tbody id="at-risk-students-table">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير الفصول -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-building me-2"></i>
                                        تقرير الفصول الدراسية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>اسم الفصل</th>
                                                    <th>المرحلة</th>
                                                    <th>المعلم المسؤول</th>
                                                    <th>عدد الطلاب</th>
                                                    <th>معدل الحضور</th>
                                                    <th>أعلى حضور</th>
                                                    <th>أقل حضور</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody id="classes-report-table">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة الإعدادات -->
                <div id="settings" class="page">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">إعدادات النظام</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-success" onclick="saveSettings()">
                                <i class="bi bi-check-circle me-1"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-building me-2"></i>
                                        معلومات المدرسة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="school-name" class="form-label">اسم المدرسة:</label>
                                        <input type="text" class="form-control" id="school-name" value="مدرسة المستقبل الابتدائية">
                                    </div>
                                    <div class="mb-3">
                                        <label for="school-address" class="form-label">عنوان المدرسة:</label>
                                        <textarea class="form-control" id="school-address" rows="2">الرياض، المملكة العربية السعودية</textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="school-phone" class="form-label">هاتف المدرسة:</label>
                                        <input type="tel" class="form-control" id="school-phone" value="011-1234567">
                                    </div>
                                    <div class="mb-3">
                                        <label for="school-email" class="form-label">البريد الإلكتروني:</label>
                                        <input type="email" class="form-control" id="school-email" value="<EMAIL>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="academic-year" class="form-label">العام الدراسي:</label>
                                        <input type="text" class="form-control" id="academic-year" value="2024-2025">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-clock me-2"></i>
                                        إعدادات الحضور
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="attendance-time" class="form-label">وقت بداية الدوام:</label>
                                        <input type="time" class="form-control" id="attendance-time" value="07:30">
                                    </div>
                                    <div class="mb-3">
                                        <label for="late-threshold" class="form-label">حد التأخير (بالدقائق):</label>
                                        <input type="number" class="form-control" id="late-threshold" value="15" min="1" max="60">
                                    </div>
                                    <div class="mb-3">
                                        <label for="absence-threshold" class="form-label">حد الغياب للتنبيه (%):</label>
                                        <input type="number" class="form-control" id="absence-threshold" value="25" min="1" max="50">
                                    </div>
                                    <div class="mb-3">
                                        <label for="semester-start" class="form-label">بداية الفصل الدراسي:</label>
                                        <input type="date" class="form-control" id="semester-start">
                                    </div>
                                    <div class="mb-3">
                                        <label for="semester-end" class="form-label">نهاية الفصل الدراسي:</label>
                                        <input type="date" class="form-control" id="semester-end">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات النظام -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-palette me-2"></i>
                                        إعدادات العرض
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="theme-select" class="form-label">نمط العرض:</label>
                                        <select class="form-select" id="theme-select">
                                            <option value="light">فاتح</option>
                                            <option value="dark">داكن</option>
                                            <option value="auto">تلقائي</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="language-select" class="form-label">اللغة:</label>
                                        <select class="form-select" id="language-select">
                                            <option value="ar">العربية</option>
                                            <option value="en">الإنجليزية</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="records-per-page" class="form-label">عدد السجلات في الصفحة:</label>
                                        <select class="form-select" id="records-per-page">
                                            <option value="10">10</option>
                                            <option value="25" selected>25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="auto-save" checked>
                                        <label class="form-check-label" for="auto-save">
                                            الحفظ التلقائي
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-database me-2"></i>
                                        إدارة البيانات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-success w-100 mb-2" onclick="exportAllData()">
                                            <i class="bi bi-download me-1"></i>تصدير جميع البيانات (JSON)
                                        </button>
                                        <small class="text-muted">تصدير البيانات بصيغة JSON للنسخ الاحتياطي</small>
                                    </div>
                                    <div class="mb-3">
                                        <input type="file" class="form-control mb-2" id="import-file" accept=".json">
                                        <button type="button" class="btn btn-warning w-100" onclick="importData()">
                                            <i class="bi bi-upload me-1"></i>استيراد البيانات
                                        </button>
                                        <small class="text-muted">استيراد البيانات من ملف JSON</small>
                                    </div>
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-info w-100 mb-2" onclick="generateSampleData()">
                                            <i class="bi bi-plus-square me-1"></i>إنشاء بيانات تجريبية
                                        </button>
                                        <small class="text-muted">إضافة بيانات تجريبية للاختبار</small>
                                    </div>
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-danger w-100" onclick="resetAllData()">
                                            <i class="bi bi-trash me-1"></i>مسح جميع البيانات
                                        </button>
                                        <small class="text-muted">حذف جميع البيانات نهائياً</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات النظام -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-info-circle me-2"></i>
                                        إحصائيات النظام
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-2">
                                            <h4 id="system-students-count">0</h4>
                                            <small class="text-muted">إجمالي الطلاب</small>
                                        </div>
                                        <div class="col-md-2">
                                            <h4 id="system-teachers-count">0</h4>
                                            <small class="text-muted">إجمالي المعلمين</small>
                                        </div>
                                        <div class="col-md-2">
                                            <h4 id="system-classes-count">0</h4>
                                            <small class="text-muted">إجمالي الفصول</small>
                                        </div>
                                        <div class="col-md-2">
                                            <h4 id="system-attendance-records">0</h4>
                                            <small class="text-muted">سجلات الحضور</small>
                                        </div>
                                        <div class="col-md-2">
                                            <h4 id="system-data-size">0 KB</h4>
                                            <small class="text-muted">حجم البيانات</small>
                                        </div>
                                        <div class="col-md-2">
                                            <h4 id="system-last-backup">لا يوجد</h4>
                                            <small class="text-muted">آخر نسخة احتياطية</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة نظام الغياب -->
                <div id="attendance" class="page">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">نظام الغياب والحضور</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <input type="date" class="form-control" id="attendance-date-picker" value="">
                            </div>
                            <button type="button" class="btn btn-primary" onclick="markAllPresent()">
                                <i class="bi bi-check-all me-1"></i>تسجيل الكل حاضر
                            </button>
                        </div>
                    </div>

                    <!-- اختيار الفصل -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <label for="attendance-class-select" class="form-label">اختر الفصل:</label>
                            <select class="form-select" id="attendance-class-select" onchange="loadAttendanceForClass()">
                                <option value="">اختر فصل...</option>
                            </select>
                        </div>
                        <div class="col-md-8">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <h4 id="present-count">0</h4>
                                            <small>حاضر</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body">
                                            <h4 id="absent-count">0</h4>
                                            <small>غائب</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body">
                                            <h4 id="late-count">0</h4>
                                            <small>متأخر</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <h4 id="attendance-percentage">0%</h4>
                                            <small>نسبة الحضور</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الحضور -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرقم</th>
                                            <th>اسم الطالب</th>
                                            <th>الصف</th>
                                            <th>حالة الحضور</th>
                                            <th>وقت الوصول</th>
                                            <th>ملاحظات</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="attendance-table">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- نافذة إضافة طالب -->
    <div class="modal fade" id="addStudentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة طالب جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="student-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="student-name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="student-name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-id" class="form-label">الرقم التسلسلي *</label>
                                <input type="text" class="form-control" id="student-id" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-grade" class="form-label">الصف *</label>
                                <select class="form-select" id="student-grade" required>
                                    <option value="">اختر الصف</option>
                                    <option value="الأول">الأول</option>
                                    <option value="الثاني">الثاني</option>
                                    <option value="الثالث">الثالث</option>
                                    <option value="الرابع">الرابع</option>
                                    <option value="الخامس">الخامس</option>
                                    <option value="السادس">السادس</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-class" class="form-label">الفصل *</label>
                                <select class="form-select" id="student-class" required>
                                    <option value="">اختر الفصل</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-age" class="form-label">العمر *</label>
                                <input type="number" class="form-control" id="student-age" min="5" max="18" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-gender" class="form-label">الجنس *</label>
                                <select class="form-select" id="student-gender" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="ذكر">ذكر</option>
                                    <option value="أنثى">أنثى</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-phone" class="form-label">هاتف ولي الأمر *</label>
                                <input type="tel" class="form-control" id="student-phone" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="student-email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="student-email">
                            </div>
                            <div class="col-12 mb-3">
                                <label for="student-address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="student-address" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="addStudent()">إضافة الطالب</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة معلم -->
    <div class="modal fade" id="addTeacherModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة معلم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="teacher-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="teacher-name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="teacher-name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="teacher-id" class="form-label">رقم الهوية *</label>
                                <input type="text" class="form-control" id="teacher-id" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="teacher-subject" class="form-label">المادة التخصصية *</label>
                                <select class="form-select" id="teacher-subject" required>
                                    <option value="">اختر المادة</option>
                                    <option value="اللغة العربية">اللغة العربية</option>
                                    <option value="الرياضيات">الرياضيات</option>
                                    <option value="العلوم">العلوم</option>
                                    <option value="الدراسات الاجتماعية">الدراسات الاجتماعية</option>
                                    <option value="اللغة الإنجليزية">اللغة الإنجليزية</option>
                                    <option value="التربية الإسلامية">التربية الإسلامية</option>
                                    <option value="التربية الفنية">التربية الفنية</option>
                                    <option value="التربية البدنية">التربية البدنية</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="teacher-phone" class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" id="teacher-phone" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="teacher-email" class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="teacher-email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="teacher-qualification" class="form-label">المؤهل العلمي</label>
                                <select class="form-select" id="teacher-qualification">
                                    <option value="">اختر المؤهل</option>
                                    <option value="بكالوريوس">بكالوريوس</option>
                                    <option value="ماجستير">ماجستير</option>
                                    <option value="دكتوراه">دكتوراه</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="teacher-experience" class="form-label">سنوات الخبرة</label>
                                <input type="number" class="form-control" id="teacher-experience" min="0" max="50">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="teacher-salary" class="form-label">الراتب</label>
                                <input type="number" class="form-control" id="teacher-salary" min="0">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="addTeacher()">إضافة المعلم</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة فصل -->
    <div class="modal fade" id="addClassModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة فصل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="class-form">
                        <div class="mb-3">
                            <label for="class-name" class="form-label">اسم الفصل *</label>
                            <input type="text" class="form-control" id="class-name" placeholder="مثال: 1أ" required>
                        </div>
                        <div class="mb-3">
                            <label for="class-grade" class="form-label">المرحلة الدراسية *</label>
                            <select class="form-select" id="class-grade" required>
                                <option value="">اختر المرحلة</option>
                                <option value="الأول">الأول</option>
                                <option value="الثاني">الثاني</option>
                                <option value="الثالث">الثالث</option>
                                <option value="الرابع">الرابع</option>
                                <option value="الخامس">الخامس</option>
                                <option value="السادس">السادس</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="class-teacher" class="form-label">المعلم المسؤول *</label>
                            <select class="form-select" id="class-teacher" required>
                                <option value="">اختر المعلم</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="class-room" class="form-label">رقم القاعة *</label>
                            <input type="text" class="form-control" id="class-room" placeholder="مثال: 101" required>
                        </div>
                        <div class="mb-3">
                            <label for="class-capacity" class="form-label">السعة القصوى</label>
                            <input type="number" class="form-control" id="class-capacity" min="1" max="50" value="30">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="addClass()">إضافة الفصل</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script>
        // البيانات المحلية
        let students = JSON.parse(localStorage.getItem('students') || '[]');
        let teachers = JSON.parse(localStorage.getItem('teachers') || '[]');
        let classes = JSON.parse(localStorage.getItem('classes') || '[]');
        let attendance = JSON.parse(localStorage.getItem('attendance') || '{}');
        let settings = JSON.parse(localStorage.getItem('settings') || '{"schoolName": "مدرسة المستقبل الابتدائية", "academicYear": "2024-2025", "attendanceTime": "07:30", "lateThreshold": 15}');

        // متغيرات الرسوم البيانية
        let studentsChart, attendanceChart, reportChart;

        // عرض الصفحات
        function showPage(pageId) {
            // إخفاء جميع الصفحات
            document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));

            // إظهار الصفحة المطلوبة
            document.getElementById(pageId).classList.add('active');
            event.target.classList.add('active');

            // تحديث المحتوى حسب الصفحة
            switch(pageId) {
                case 'dashboard':
                    updateDashboard();
                    break;
                case 'students':
                    updateStudentsTable();
                    updateFilters();
                    break;
                case 'teachers':
                    updateTeachersTable();
                    updateTeacherFilters();
                    break;
                case 'classes':
                    updateClassesTable();
                    break;
                case 'attendance':
                    updateAttendancePage();
                    break;
                case 'reports':
                    updateReportsPage();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        }

        // حفظ البيانات
        function saveData() {
            localStorage.setItem('students', JSON.stringify(students));
            localStorage.setItem('teachers', JSON.stringify(teachers));
            localStorage.setItem('classes', JSON.stringify(classes));
            localStorage.setItem('attendance', JSON.stringify(attendance));
            localStorage.setItem('settings', JSON.stringify(settings));
        }

        // تحديث لوحة التحكم
        function updateDashboard() {
            // تحديث العدادات
            document.getElementById('dashboard-students-count').textContent = students.length;
            document.getElementById('dashboard-teachers-count').textContent = teachers.length;
            document.getElementById('dashboard-classes-count').textContent = classes.length;

            // حساب معدل الحضور اليوم
            const today = new Date().toISOString().split('T')[0];
            const todayAttendance = attendance[today] || {};
            const totalStudents = students.length;
            const presentStudents = Object.values(todayAttendance).filter(status => status === 'present' || status === 'late').length;
            const attendanceRate = totalStudents > 0 ? Math.round((presentStudents / totalStudents) * 100) : 0;

            document.getElementById('dashboard-attendance-rate').textContent = attendanceRate + '%';
            document.getElementById('attendance-date').textContent = new Date().toLocaleDateString('ar-SA');

            // تحديث الرسوم البيانية
            updateDashboardCharts();
        }

        // تحديث الرسوم البيانية في لوحة التحكم
        function updateDashboardCharts() {
            // رسم بياني لتوزيع الطلاب حسب الصف
            const gradeData = {};
            students.forEach(student => {
                gradeData[student.grade] = (gradeData[student.grade] || 0) + 1;
            });

            const ctx1 = document.getElementById('studentsChart');
            if (studentsChart) studentsChart.destroy();
            studentsChart = new Chart(ctx1, {
                type: 'bar',
                data: {
                    labels: Object.keys(gradeData),
                    datasets: [{
                        label: 'عدد الطلاب',
                        data: Object.values(gradeData),
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

            // رسم بياني لمعدل الحضور الأسبوعي
            const weeklyAttendance = getWeeklyAttendanceData();
            const ctx2 = document.getElementById('attendanceChart');
            if (attendanceChart) attendanceChart.destroy();
            attendanceChart = new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: weeklyAttendance.labels,
                    datasets: [{
                        label: 'نسبة الحضور %',
                        data: weeklyAttendance.data,
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        // الحصول على بيانات الحضور الأسبوعي
        function getWeeklyAttendanceData() {
            const labels = [];
            const data = [];
            const today = new Date();

            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];
                const dayName = date.toLocaleDateString('ar-SA', { weekday: 'short' });

                labels.push(dayName);

                const dayAttendance = attendance[dateStr] || {};
                const totalStudents = students.length;
                const presentStudents = Object.values(dayAttendance).filter(status => status === 'present' || status === 'late').length;
                const rate = totalStudents > 0 ? Math.round((presentStudents / totalStudents) * 100) : 0;

                data.push(rate);
            }

            return { labels, data };
        }

        // إضافة طالب جديد
        function addStudent() {
            const student = {
                id: Date.now(),
                studentId: document.getElementById('student-id').value,
                name: document.getElementById('student-name').value,
                grade: document.getElementById('student-grade').value,
                class: document.getElementById('student-class').value,
                age: parseInt(document.getElementById('student-age').value),
                gender: document.getElementById('student-gender').value,
                phone: document.getElementById('student-phone').value,
                email: document.getElementById('student-email').value,
                address: document.getElementById('student-address').value,
                attendanceRate: 0,
                createdAt: new Date().toISOString()
            };

            // التحقق من عدم تكرار الرقم التسلسلي
            if (students.some(s => s.studentId === student.studentId)) {
                alert('الرقم التسلسلي موجود مسبقاً!');
                return;
            }

            students.push(student);
            saveData();
            updateStudentsTable();
            updateDashboard();

            // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
            bootstrap.Modal.getInstance(document.getElementById('addStudentModal')).hide();
            document.getElementById('student-form').reset();

            showNotification('تم إضافة الطالب بنجاح!', 'success');
        }

        // تحديث جدول الطلاب
        function updateStudentsTable() {
            const tbody = document.getElementById('students-table');
            tbody.innerHTML = '';

            let filteredStudents = students;

            // تطبيق الفلاتر
            const searchTerm = document.getElementById('student-search')?.value.toLowerCase() || '';
            const gradeFilter = document.getElementById('student-grade-filter')?.value || '';
            const classFilter = document.getElementById('student-class-filter')?.value || '';

            if (searchTerm) {
                filteredStudents = filteredStudents.filter(student =>
                    student.name.toLowerCase().includes(searchTerm) ||
                    student.studentId.toLowerCase().includes(searchTerm)
                );
            }

            if (gradeFilter) {
                filteredStudents = filteredStudents.filter(student => student.grade === gradeFilter);
            }

            if (classFilter) {
                filteredStudents = filteredStudents.filter(student => student.class === classFilter);
            }

            filteredStudents.forEach(student => {
                const attendanceRate = calculateStudentAttendanceRate(student.id);
                tbody.innerHTML += `
                    <tr>
                        <td>${student.studentId}</td>
                        <td>${student.name}</td>
                        <td>${student.grade}</td>
                        <td>${student.class || 'غير محدد'}</td>
                        <td>${student.age}</td>
                        <td>${student.phone}</td>
                        <td>
                            <span class="badge ${attendanceRate >= 90 ? 'bg-success' : attendanceRate >= 75 ? 'bg-warning' : 'bg-danger'}">
                                ${attendanceRate}%
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editStudent(${student.id})">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteStudent(${student.id})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        // حساب معدل حضور الطالب
        function calculateStudentAttendanceRate(studentId) {
            let totalDays = 0;
            let presentDays = 0;

            Object.keys(attendance).forEach(date => {
                if (attendance[date][studentId]) {
                    totalDays++;
                    if (attendance[date][studentId] === 'present' || attendance[date][studentId] === 'late') {
                        presentDays++;
                    }
                }
            });

            return totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;
        }

        // إضافة معلم جديد
        function addTeacher() {
            const teacher = {
                id: Date.now(),
                teacherId: document.getElementById('teacher-id').value,
                name: document.getElementById('teacher-name').value,
                subject: document.getElementById('teacher-subject').value,
                phone: document.getElementById('teacher-phone').value,
                email: document.getElementById('teacher-email').value,
                qualification: document.getElementById('teacher-qualification').value,
                experience: parseInt(document.getElementById('teacher-experience').value) || 0,
                salary: parseFloat(document.getElementById('teacher-salary').value) || 0,
                createdAt: new Date().toISOString()
            };

            // التحقق من عدم تكرار رقم الهوية
            if (teachers.some(t => t.teacherId === teacher.teacherId)) {
                alert('رقم الهوية موجود مسبقاً!');
                return;
            }

            teachers.push(teacher);
            saveData();
            updateTeachersTable();
            updateTeacherOptions();
            updateDashboard();

            // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
            bootstrap.Modal.getInstance(document.getElementById('addTeacherModal')).hide();
            document.getElementById('teacher-form').reset();

            showNotification('تم إضافة المعلم بنجاح!', 'success');
        }

        // تحديث جدول المعلمين
        function updateTeachersTable() {
            const tbody = document.getElementById('teachers-table');
            tbody.innerHTML = '';

            let filteredTeachers = teachers;

            // تطبيق الفلاتر
            const searchTerm = document.getElementById('teacher-search')?.value.toLowerCase() || '';
            const subjectFilter = document.getElementById('teacher-subject-filter')?.value || '';

            if (searchTerm) {
                filteredTeachers = filteredTeachers.filter(teacher =>
                    teacher.name.toLowerCase().includes(searchTerm) ||
                    teacher.teacherId.toLowerCase().includes(searchTerm)
                );
            }

            if (subjectFilter) {
                filteredTeachers = filteredTeachers.filter(teacher => teacher.subject === subjectFilter);
            }

            filteredTeachers.forEach(teacher => {
                const assignedClasses = classes.filter(c => c.teacher === teacher.name).map(c => c.name).join(', ') || 'لا يوجد';
                tbody.innerHTML += `
                    <tr>
                        <td>${teacher.teacherId}</td>
                        <td>${teacher.name}</td>
                        <td>${teacher.subject}</td>
                        <td>${teacher.phone}</td>
                        <td>${teacher.email}</td>
                        <td>${assignedClasses}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editTeacher(${teacher.id})">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteTeacher(${teacher.id})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        // إضافة فصل جديد
        function addClass() {
            const classData = {
                id: Date.now(),
                name: document.getElementById('class-name').value,
                grade: document.getElementById('class-grade').value,
                teacher: document.getElementById('class-teacher').value,
                room: document.getElementById('class-room').value,
                capacity: parseInt(document.getElementById('class-capacity').value) || 30,
                createdAt: new Date().toISOString()
            };

            // التحقق من عدم تكرار اسم الفصل
            if (classes.some(c => c.name === classData.name)) {
                alert('اسم الفصل موجود مسبقاً!');
                return;
            }

            classes.push(classData);
            saveData();
            updateClassesTable();
            updateClassOptions();
            updateDashboard();

            // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
            bootstrap.Modal.getInstance(document.getElementById('addClassModal')).hide();
            document.getElementById('class-form').reset();

            showNotification('تم إضافة الفصل بنجاح!', 'success');
        }

        // تحديث جدول الفصول
        function updateClassesTable() {
            const tbody = document.getElementById('classes-table');
            tbody.innerHTML = '';

            classes.forEach(classData => {
                const studentsInClass = students.filter(s => s.class === classData.name).length;
                const classAttendanceRate = calculateClassAttendanceRate(classData.name);
                tbody.innerHTML += `
                    <tr>
                        <td>${classData.id}</td>
                        <td>${classData.name}</td>
                        <td>${classData.grade}</td>
                        <td>${classData.teacher}</td>
                        <td>${classData.room}</td>
                        <td>${studentsInClass}/${classData.capacity}</td>
                        <td>
                            <span class="badge ${classAttendanceRate >= 90 ? 'bg-success' : classAttendanceRate >= 75 ? 'bg-warning' : 'bg-danger'}">
                                ${classAttendanceRate}%
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editClass(${classData.id})">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteClass(${classData.id})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        // حساب معدل حضور الفصل
        function calculateClassAttendanceRate(className) {
            const classStudents = students.filter(s => s.class === className);
            if (classStudents.length === 0) return 0;

            let totalRate = 0;
            classStudents.forEach(student => {
                totalRate += calculateStudentAttendanceRate(student.id);
            });

            return Math.round(totalRate / classStudents.length);
        }

        // تحديث صفحة الغياب
        function updateAttendancePage() {
            // تعيين التاريخ الحالي
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('attendance-date-picker').value = today;

            // تحديث قائمة الفصول
            updateAttendanceClassOptions();
        }

        // تحديث خيارات الفصول في صفحة الغياب
        function updateAttendanceClassOptions() {
            const select = document.getElementById('attendance-class-select');
            select.innerHTML = '<option value="">اختر فصل...</option>';
            classes.forEach(classData => {
                select.innerHTML += `<option value="${classData.name}">${classData.name} - ${classData.grade}</option>`;
            });
        }

        // تحميل الحضور للفصل المحدد
        function loadAttendanceForClass() {
            const selectedClass = document.getElementById('attendance-class-select').value;
            const selectedDate = document.getElementById('attendance-date-picker').value;

            if (!selectedClass || !selectedDate) return;

            const classStudents = students.filter(s => s.class === selectedClass);
            const tbody = document.getElementById('attendance-table');
            tbody.innerHTML = '';

            let presentCount = 0, absentCount = 0, lateCount = 0;

            classStudents.forEach(student => {
                const attendanceStatus = attendance[selectedDate]?.[student.id] || 'absent';
                const arrivalTime = attendance[selectedDate]?.[student.id + '_time'] || '';

                if (attendanceStatus === 'present') presentCount++;
                else if (attendanceStatus === 'absent') absentCount++;
                else if (attendanceStatus === 'late') lateCount++;

                tbody.innerHTML += `
                    <tr>
                        <td>${student.studentId}</td>
                        <td>${student.name}</td>
                        <td>${student.grade}</td>
                        <td>
                            <select class="form-select form-select-sm" onchange="updateAttendanceStatus(${student.id}, '${selectedDate}', this.value)">
                                <option value="present" ${attendanceStatus === 'present' ? 'selected' : ''}>حاضر</option>
                                <option value="absent" ${attendanceStatus === 'absent' ? 'selected' : ''}>غائب</option>
                                <option value="late" ${attendanceStatus === 'late' ? 'selected' : ''}>متأخر</option>
                            </select>
                        </td>
                        <td>
                            <input type="time" class="form-control form-control-sm" value="${arrivalTime}"
                                   onchange="updateArrivalTime(${student.id}, '${selectedDate}', this.value)">
                        </td>
                        <td>
                            <input type="text" class="form-control form-control-sm" placeholder="ملاحظات..."
                                   value="${attendance[selectedDate]?.[student.id + '_note'] || ''}"
                                   onchange="updateAttendanceNote(${student.id}, '${selectedDate}', this.value)">
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-info" onclick="viewStudentAttendanceHistory(${student.id})">
                                <i class="bi bi-clock-history"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            // تحديث الإحصائيات
            document.getElementById('present-count').textContent = presentCount;
            document.getElementById('absent-count').textContent = absentCount;
            document.getElementById('late-count').textContent = lateCount;

            const total = classStudents.length;
            const attendancePercentage = total > 0 ? Math.round(((presentCount + lateCount) / total) * 100) : 0;
            document.getElementById('attendance-percentage').textContent = attendancePercentage + '%';
        }

        // تحديث حالة الحضور
        function updateAttendanceStatus(studentId, date, status) {
            if (!attendance[date]) attendance[date] = {};
            attendance[date][studentId] = status;

            // إذا كان الطالب حاضر أو متأخر، تسجيل الوقت الحالي
            if (status === 'present' || status === 'late') {
                const now = new Date();
                attendance[date][studentId + '_time'] = now.toTimeString().slice(0, 5);
            }

            saveData();
            loadAttendanceForClass(); // إعادة تحميل الجدول لتحديث الإحصائيات
            updateDashboard(); // تحديث لوحة التحكم
        }

        // تحديث وقت الوصول
        function updateArrivalTime(studentId, date, time) {
            if (!attendance[date]) attendance[date] = {};
            attendance[date][studentId + '_time'] = time;
            saveData();
        }

        // تحديث ملاحظة الحضور
        function updateAttendanceNote(studentId, date, note) {
            if (!attendance[date]) attendance[date] = {};
            attendance[date][studentId + '_note'] = note;
            saveData();
        }

        // تسجيل الكل حاضر
        function markAllPresent() {
            const selectedClass = document.getElementById('attendance-class-select').value;
            const selectedDate = document.getElementById('attendance-date-picker').value;

            if (!selectedClass || !selectedDate) {
                alert('يرجى اختيار الفصل والتاريخ أولاً');
                return;
            }

            const classStudents = students.filter(s => s.class === selectedClass);
            const now = new Date();
            const currentTime = now.toTimeString().slice(0, 5);

            if (!attendance[selectedDate]) attendance[selectedDate] = {};

            classStudents.forEach(student => {
                attendance[selectedDate][student.id] = 'present';
                attendance[selectedDate][student.id + '_time'] = currentTime;
            });

            saveData();
            loadAttendanceForClass();
            updateDashboard();
            showNotification('تم تسجيل جميع الطلاب كحاضرين!', 'success');
        }

        // تحديث خيارات المعلمين
        function updateTeacherOptions() {
            const selects = document.querySelectorAll('#class-teacher, #student-class');
            selects.forEach(select => {
                if (select.id === 'class-teacher') {
                    select.innerHTML = '<option value="">اختر المعلم</option>';
                    teachers.forEach(teacher => {
                        select.innerHTML += `<option value="${teacher.name}">${teacher.name} - ${teacher.subject}</option>`;
                    });
                }
            });
        }

        // تحديث خيارات الفصول
        function updateClassOptions() {
            const selects = document.querySelectorAll('#student-class, #student-class-filter, #attendance-class-select, #report-class-filter');
            selects.forEach(select => {
                const currentValue = select.value;
                if (select.id === 'student-class') {
                    select.innerHTML = '<option value="">اختر الفصل</option>';
                } else if (select.id.includes('filter') || select.id.includes('select')) {
                    select.innerHTML = '<option value="">جميع الفصول</option>';
                }

                classes.forEach(classData => {
                    select.innerHTML += `<option value="${classData.name}">${classData.name} - ${classData.grade}</option>`;
                });

                select.value = currentValue;
            });
        }

        // تحديث الفلاتر
        function updateFilters() {
            // فلتر الصفوف للطلاب
            const gradeFilter = document.getElementById('student-grade-filter');
            if (gradeFilter) {
                const grades = [...new Set(students.map(s => s.grade))];
                gradeFilter.innerHTML = '<option value="">جميع الصفوف</option>';
                grades.forEach(grade => {
                    gradeFilter.innerHTML += `<option value="${grade}">${grade}</option>`;
                });
            }

            updateClassOptions();
        }

        // تحديث فلاتر المعلمين
        function updateTeacherFilters() {
            const subjectFilter = document.getElementById('teacher-subject-filter');
            if (subjectFilter) {
                const subjects = [...new Set(teachers.map(t => t.subject))];
                subjectFilter.innerHTML = '<option value="">جميع المواد</option>';
                subjects.forEach(subject => {
                    subjectFilter.innerHTML += `<option value="${subject}">${subject}</option>`;
                });
            }
        }

        // حذف طالب
        function deleteStudent(id) {
            if (confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
                students = students.filter(s => s.id !== id);

                // حذف بيانات الحضور المرتبطة
                Object.keys(attendance).forEach(date => {
                    delete attendance[date][id];
                    delete attendance[date][id + '_time'];
                    delete attendance[date][id + '_note'];
                });

                saveData();
                updateStudentsTable();
                updateDashboard();
                showNotification('تم حذف الطالب بنجاح!', 'success');
            }
        }

        // حذف معلم
        function deleteTeacher(id) {
            if (confirm('هل أنت متأكد من حذف هذا المعلم؟')) {
                teachers = teachers.filter(t => t.id !== id);
                saveData();
                updateTeachersTable();
                updateTeacherOptions();
                updateDashboard();
                showNotification('تم حذف المعلم بنجاح!', 'success');
            }
        }

        // حذف فصل
        function deleteClass(id) {
            if (confirm('هل أنت متأكد من حذف هذا الفصل؟')) {
                classes = classes.filter(c => c.id !== id);
                saveData();
                updateClassesTable();
                updateClassOptions();
                updateDashboard();
                showNotification('تم حذف الفصل بنجاح!', 'success');
            }
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // إزالة الإشعار تلقائياً بعد 3 ثوان
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // إضافة مستمعي الأحداث للبحث والفلترة
        function addEventListeners() {
            // البحث في الطلاب
            const studentSearch = document.getElementById('student-search');
            if (studentSearch) {
                studentSearch.addEventListener('input', updateStudentsTable);
            }

            // فلاتر الطلاب
            const studentFilters = document.querySelectorAll('#student-grade-filter, #student-class-filter');
            studentFilters.forEach(filter => {
                filter.addEventListener('change', updateStudentsTable);
            });

            // البحث في المعلمين
            const teacherSearch = document.getElementById('teacher-search');
            if (teacherSearch) {
                teacherSearch.addEventListener('input', updateTeachersTable);
            }

            // فلاتر المعلمين
            const teacherFilters = document.querySelectorAll('#teacher-subject-filter');
            teacherFilters.forEach(filter => {
                filter.addEventListener('change', updateTeachersTable);
            });

            // تغيير التاريخ في صفحة الحضور
            const attendanceDatePicker = document.getElementById('attendance-date-picker');
            if (attendanceDatePicker) {
                attendanceDatePicker.addEventListener('change', loadAttendanceForClass);
            }
        }

        // التهيئة الأولية
        function initializeApp() {
            // تحديث لوحة التحكم
            updateDashboard();

            // تحديث خيارات القوائم
            updateTeacherOptions();
            updateClassOptions();
            updateFilters();
            updateTeacherFilters();

            // إضافة مستمعي الأحداث
            addEventListeners();

            // تعيين التاريخ الحالي في منتقي التاريخ
            const today = new Date().toISOString().split('T')[0];
            const datePicker = document.getElementById('attendance-date-picker');
            if (datePicker) {
                datePicker.value = today;
            }
        }

        // تشغيل التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            showNotification('مرحباً بك في نظام إدارة المدرسة الاحترافي!', 'success');
        });

        // تحديث صفحة التقارير
        function updateReportsPage() {
            // تحديث الإحصائيات السريعة
            updateReportsStatistics();

            // تحديث الرسوم البيانية
            updateReportsCharts();

            // تحديث الجداول
            updateTopStudentsTable();
            updateAtRiskStudentsTable();
            updateClassesReportTable();

            // تعيين التواريخ الافتراضية
            setDefaultReportDates();
        }

        // تحديث إحصائيات التقارير
        function updateReportsStatistics() {
            // حساب أيام الحضور المسجلة
            const totalAttendanceDays = Object.keys(attendance).length;
            document.getElementById('total-attendance-days').textContent = totalAttendanceDays;

            // حساب معدل الحضور العام
            let totalPresent = 0, totalRecords = 0;
            Object.values(attendance).forEach(dayData => {
                Object.keys(dayData).forEach(key => {
                    if (!key.includes('_time') && !key.includes('_note')) {
                        totalRecords++;
                        if (dayData[key] === 'present' || dayData[key] === 'late') {
                            totalPresent++;
                        }
                    }
                });
            });
            const overallRate = totalRecords > 0 ? Math.round((totalPresent / totalRecords) * 100) : 0;
            document.getElementById('overall-attendance-rate').textContent = overallRate + '%';

            // حساب أفضل فصل في الحضور
            let bestClassRate = 0;
            classes.forEach(classData => {
                const rate = calculateClassAttendanceRate(classData.name);
                if (rate > bestClassRate) bestClassRate = rate;
            });
            document.getElementById('best-class-attendance').textContent = bestClassRate + '%';

            // حساب الطلاب في خطر
            let studentsAtRisk = 0;
            students.forEach(student => {
                const rate = calculateStudentAttendanceRate(student.id);
                if (rate < 75) studentsAtRisk++;
            });
            document.getElementById('students-at-risk').textContent = studentsAtRisk;
        }

        // تحديث رسوم التقارير البيانية
        function updateReportsCharts() {
            // رسم بياني للحضور التفصيلي
            const weeklyData = getWeeklyAttendanceData();
            const ctx1 = document.getElementById('reportChart');
            if (reportChart) reportChart.destroy();
            reportChart = new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: weeklyData.labels,
                    datasets: [{
                        label: 'نسبة الحضور %',
                        data: weeklyData.data,
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });

            // رسم بياني دائري لتوزيع حالات الحضور
            let presentCount = 0, absentCount = 0, lateCount = 0;
            Object.values(attendance).forEach(dayData => {
                Object.keys(dayData).forEach(key => {
                    if (!key.includes('_time') && !key.includes('_note')) {
                        if (dayData[key] === 'present') presentCount++;
                        else if (dayData[key] === 'absent') absentCount++;
                        else if (dayData[key] === 'late') lateCount++;
                    }
                });
            });

            const ctx2 = document.getElementById('attendanceDistributionChart');
            if (window.attendanceDistributionChart) window.attendanceDistributionChart.destroy();
            window.attendanceDistributionChart = new Chart(ctx2, {
                type: 'doughnut',
                data: {
                    labels: ['حاضر', 'غائب', 'متأخر'],
                    datasets: [{
                        data: [presentCount, absentCount, lateCount],
                        backgroundColor: [
                            'rgba(40, 167, 69, 0.8)',
                            'rgba(220, 53, 69, 0.8)',
                            'rgba(255, 193, 7, 0.8)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // تحديث جدول أفضل الطلاب
        function updateTopStudentsTable() {
            const studentsWithRates = students.map(student => ({
                ...student,
                attendanceRate: calculateStudentAttendanceRate(student.id)
            })).sort((a, b) => b.attendanceRate - a.attendanceRate).slice(0, 10);

            const tbody = document.getElementById('top-students-table');
            tbody.innerHTML = '';

            studentsWithRates.forEach((student, index) => {
                tbody.innerHTML += `
                    <tr>
                        <td>
                            <span class="badge ${index < 3 ? 'bg-warning' : 'bg-secondary'}">${index + 1}</span>
                        </td>
                        <td>${student.name}</td>
                        <td>${student.class || 'غير محدد'}</td>
                        <td>
                            <span class="badge bg-success">${student.attendanceRate}%</span>
                        </td>
                    </tr>
                `;
            });
        }

        // تحديث جدول الطلاب في خطر
        function updateAtRiskStudentsTable() {
            const atRiskStudents = students.map(student => {
                const rate = calculateStudentAttendanceRate(student.id);
                const absentDays = calculateAbsentDays(student.id);
                return { ...student, attendanceRate: rate, absentDays };
            }).filter(student => student.attendanceRate < 75)
              .sort((a, b) => a.attendanceRate - b.attendanceRate);

            const tbody = document.getElementById('at-risk-students-table');
            tbody.innerHTML = '';

            atRiskStudents.forEach(student => {
                tbody.innerHTML += `
                    <tr>
                        <td>${student.name}</td>
                        <td>${student.class || 'غير محدد'}</td>
                        <td>
                            <span class="badge bg-danger">${student.attendanceRate}%</span>
                        </td>
                        <td>${student.absentDays}</td>
                    </tr>
                `;
            });
        }

        // حساب أيام الغياب للطالب
        function calculateAbsentDays(studentId) {
            let absentDays = 0;
            Object.keys(attendance).forEach(date => {
                if (attendance[date][studentId] === 'absent') {
                    absentDays++;
                }
            });
            return absentDays;
        }

        // تحديث جدول تقرير الفصول
        function updateClassesReportTable() {
            const tbody = document.getElementById('classes-report-table');
            tbody.innerHTML = '';

            classes.forEach(classData => {
                const classStudents = students.filter(s => s.class === classData.name);
                const attendanceRates = classStudents.map(s => calculateStudentAttendanceRate(s.id));
                const avgRate = attendanceRates.length > 0 ? Math.round(attendanceRates.reduce((a, b) => a + b, 0) / attendanceRates.length) : 0;
                const maxRate = attendanceRates.length > 0 ? Math.max(...attendanceRates) : 0;
                const minRate = attendanceRates.length > 0 ? Math.min(...attendanceRates) : 0;

                let status = 'ممتاز';
                let statusClass = 'bg-success';
                if (avgRate < 90) { status = 'جيد'; statusClass = 'bg-primary'; }
                if (avgRate < 80) { status = 'مقبول'; statusClass = 'bg-warning'; }
                if (avgRate < 70) { status = 'يحتاج تحسين'; statusClass = 'bg-danger'; }

                tbody.innerHTML += `
                    <tr>
                        <td>${classData.name}</td>
                        <td>${classData.grade}</td>
                        <td>${classData.teacher}</td>
                        <td>${classStudents.length}</td>
                        <td>
                            <span class="badge ${avgRate >= 90 ? 'bg-success' : avgRate >= 75 ? 'bg-warning' : 'bg-danger'}">
                                ${avgRate}%
                            </span>
                        </td>
                        <td>${maxRate}%</td>
                        <td>${minRate}%</td>
                        <td>
                            <span class="badge ${statusClass}">${status}</span>
                        </td>
                    </tr>
                `;
            });
        }

        // تعيين التواريخ الافتراضية للتقارير
        function setDefaultReportDates() {
            const today = new Date();
            const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

            document.getElementById('report-start-date').value = oneMonthAgo.toISOString().split('T')[0];
            document.getElementById('report-end-date').value = today.toISOString().split('T')[0];
        }

        // تحديث صفحة الإعدادات
        function loadSettings() {
            // تحميل الإعدادات المحفوظة
            const savedSettings = JSON.parse(localStorage.getItem('settings') || '{}');

            // تحديث حقول معلومات المدرسة
            document.getElementById('school-name').value = savedSettings.schoolName || 'مدرسة المستقبل الابتدائية';
            document.getElementById('school-address').value = savedSettings.schoolAddress || 'الرياض، المملكة العربية السعودية';
            document.getElementById('school-phone').value = savedSettings.schoolPhone || '011-1234567';
            document.getElementById('school-email').value = savedSettings.schoolEmail || '<EMAIL>';
            document.getElementById('academic-year').value = savedSettings.academicYear || '2024-2025';

            // تحديث إعدادات الحضور
            document.getElementById('attendance-time').value = savedSettings.attendanceTime || '07:30';
            document.getElementById('late-threshold').value = savedSettings.lateThreshold || 15;
            document.getElementById('absence-threshold').value = savedSettings.absenceThreshold || 25;
            document.getElementById('semester-start').value = savedSettings.semesterStart || '';
            document.getElementById('semester-end').value = savedSettings.semesterEnd || '';

            // تحديث إعدادات العرض
            document.getElementById('theme-select').value = savedSettings.theme || 'light';
            document.getElementById('language-select').value = savedSettings.language || 'ar';
            document.getElementById('records-per-page').value = savedSettings.recordsPerPage || 25;
            document.getElementById('auto-save').checked = savedSettings.autoSave !== false;

            // تحديث إحصائيات النظام
            updateSystemStatistics();
        }

        // تحديث إحصائيات النظام
        function updateSystemStatistics() {
            document.getElementById('system-students-count').textContent = students.length;
            document.getElementById('system-teachers-count').textContent = teachers.length;
            document.getElementById('system-classes-count').textContent = classes.length;

            // حساب عدد سجلات الحضور
            let attendanceRecords = 0;
            Object.values(attendance).forEach(dayData => {
                Object.keys(dayData).forEach(key => {
                    if (!key.includes('_time') && !key.includes('_note')) {
                        attendanceRecords++;
                    }
                });
            });
            document.getElementById('system-attendance-records').textContent = attendanceRecords;

            // حساب حجم البيانات
            const dataSize = JSON.stringify({students, teachers, classes, attendance}).length;
            const sizeInKB = Math.round(dataSize / 1024);
            document.getElementById('system-data-size').textContent = sizeInKB + ' KB';

            // آخر نسخة احتياطية
            const lastBackup = localStorage.getItem('lastBackup');
            document.getElementById('system-last-backup').textContent = lastBackup ?
                new Date(lastBackup).toLocaleDateString('ar-SA') : 'لا يوجد';
        }

        // حفظ الإعدادات
        function saveSettings() {
            const newSettings = {
                schoolName: document.getElementById('school-name').value,
                schoolAddress: document.getElementById('school-address').value,
                schoolPhone: document.getElementById('school-phone').value,
                schoolEmail: document.getElementById('school-email').value,
                academicYear: document.getElementById('academic-year').value,
                attendanceTime: document.getElementById('attendance-time').value,
                lateThreshold: parseInt(document.getElementById('late-threshold').value),
                absenceThreshold: parseInt(document.getElementById('absence-threshold').value),
                semesterStart: document.getElementById('semester-start').value,
                semesterEnd: document.getElementById('semester-end').value,
                theme: document.getElementById('theme-select').value,
                language: document.getElementById('language-select').value,
                recordsPerPage: parseInt(document.getElementById('records-per-page').value),
                autoSave: document.getElementById('auto-save').checked
            };

            localStorage.setItem('settings', JSON.stringify(newSettings));
            settings = newSettings;
            showNotification('تم حفظ الإعدادات بنجاح!', 'success');
        }

        // تصدير جميع البيانات
        function exportAllData() {
            const allData = {
                students,
                teachers,
                classes,
                attendance,
                settings,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(allData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `school_data_backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            // تحديث تاريخ آخر نسخة احتياطية
            localStorage.setItem('lastBackup', new Date().toISOString());
            updateSystemStatistics();

            showNotification('تم تصدير البيانات بنجاح!', 'success');
        }

        // استيراد البيانات
        function importData() {
            const fileInput = document.getElementById('import-file');
            const file = fileInput.files[0];

            if (!file) {
                alert('يرجى اختيار ملف للاستيراد');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);

                    // التحقق من صحة البيانات
                    if (!importedData.students || !importedData.teachers || !importedData.classes) {
                        throw new Error('ملف البيانات غير صحيح');
                    }

                    if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                        students = importedData.students || [];
                        teachers = importedData.teachers || [];
                        classes = importedData.classes || [];
                        attendance = importedData.attendance || {};
                        settings = importedData.settings || settings;

                        saveData();
                        initializeApp();
                        showNotification('تم استيراد البيانات بنجاح!', 'success');
                    }
                } catch (error) {
                    alert('خطأ في قراءة الملف: ' + error.message);
                }
            };
            reader.readAsText(file);
        }

        // إنشاء بيانات تجريبية
        function generateSampleData() {
            if (confirm('هل تريد إضافة بيانات تجريبية؟ سيتم إضافة طلاب ومعلمين وفصول للاختبار.')) {
                // إضافة معلمين تجريبيين
                const sampleTeachers = [
                    { id: Date.now() + 1, teacherId: 'T001', name: 'أحمد محمد علي', subject: 'اللغة العربية', phone: '0501234567', email: '<EMAIL>', qualification: 'بكالوريوس', experience: 5, salary: 8000 },
                    { id: Date.now() + 2, teacherId: 'T002', name: 'فاطمة سالم', subject: 'الرياضيات', phone: '0507654321', email: '<EMAIL>', qualification: 'ماجستير', experience: 8, salary: 9000 },
                    { id: Date.now() + 3, teacherId: 'T003', name: 'محمد عبدالله', subject: 'العلوم', phone: '0509876543', email: '<EMAIL>', qualification: 'بكالوريوس', experience: 3, salary: 7500 }
                ];

                // إضافة فصول تجريبية
                const sampleClasses = [
                    { id: Date.now() + 1, name: '1أ', grade: 'الأول', teacher: 'أحمد محمد علي', room: '101', capacity: 25 },
                    { id: Date.now() + 2, name: '2ب', grade: 'الثاني', teacher: 'فاطمة سالم', room: '102', capacity: 30 },
                    { id: Date.now() + 3, name: '3ج', grade: 'الثالث', teacher: 'محمد عبدالله', room: '103', capacity: 28 }
                ];

                // إضافة طلاب تجريبيين
                const sampleStudents = [
                    { id: Date.now() + 1, studentId: 'S001', name: 'علي أحمد محمد', grade: 'الأول', class: '1أ', age: 7, gender: 'ذكر', phone: '0551234567', email: '', address: 'الرياض' },
                    { id: Date.now() + 2, studentId: 'S002', name: 'نورا سعد علي', grade: 'الأول', class: '1أ', age: 6, gender: 'أنثى', phone: '0557654321', email: '', address: 'الرياض' },
                    { id: Date.now() + 3, studentId: 'S003', name: 'خالد محمد سالم', grade: 'الثاني', class: '2ب', age: 8, gender: 'ذكر', phone: '0559876543', email: '', address: 'الرياض' },
                    { id: Date.now() + 4, studentId: 'S004', name: 'مريم عبدالله أحمد', grade: 'الثاني', class: '2ب', age: 7, gender: 'أنثى', phone: '0552468135', email: '', address: 'الرياض' },
                    { id: Date.now() + 5, studentId: 'S005', name: 'يوسف سالم محمد', grade: 'الثالث', class: '3ج', age: 9, gender: 'ذكر', phone: '0558642097', email: '', address: 'الرياض' }
                ];

                // دمج البيانات التجريبية مع البيانات الحالية
                teachers.push(...sampleTeachers);
                classes.push(...sampleClasses);
                students.push(...sampleStudents);

                // إنشاء بيانات حضور تجريبية للأسبوع الماضي
                const today = new Date();
                for (let i = 7; i >= 1; i--) {
                    const date = new Date(today);
                    date.setDate(date.getDate() - i);
                    const dateStr = date.toISOString().split('T')[0];

                    if (!attendance[dateStr]) attendance[dateStr] = {};

                    sampleStudents.forEach(student => {
                        const random = Math.random();
                        if (random > 0.1) { // 90% حضور
                            attendance[dateStr][student.id] = random > 0.05 ? 'present' : 'late';
                            attendance[dateStr][student.id + '_time'] = random > 0.05 ? '07:30' : '07:45';
                        } else {
                            attendance[dateStr][student.id] = 'absent';
                        }
                    });
                }

                saveData();
                initializeApp();
                showNotification('تم إنشاء البيانات التجريبية بنجاح!', 'success');
            }
        }

        // وظائف التقارير
        function generateReport(type) {
            showNotification(`جاري إنشاء التقرير ${type}...`, 'info');
            updateReportsPage();
        }

        function generateCustomReport() {
            const startDate = document.getElementById('report-start-date').value;
            const endDate = document.getElementById('report-end-date').value;
            const classFilter = document.getElementById('report-class-filter').value;

            if (!startDate || !endDate) {
                alert('يرجى تحديد تاريخ البداية والنهاية');
                return;
            }

            showNotification('جاري إنشاء التقرير المخصص...', 'info');
            updateReportsPage();
        }

        function exportReportToPDF() {
            showNotification('ميزة تصدير PDF قيد التطوير', 'info');
        }

        // وظائف إضافية
        function editStudent(id) { showNotification('ميزة التعديل قيد التطوير', 'info'); }
        function editTeacher(id) { showNotification('ميزة التعديل قيد التطوير', 'info'); }
        function editClass(id) { showNotification('ميزة التعديل قيد التطوير', 'info'); }
        function viewStudentAttendanceHistory(id) { showNotification('ميزة عرض تاريخ الحضور قيد التطوير', 'info'); }
        function exportData() { showNotification('ميزة التصدير قيد التطوير', 'info'); }
        function printReport() { showNotification('ميزة الطباعة قيد التطوير', 'info'); }

        function resetAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                localStorage.clear();
                location.reload();
            }
        }
    </script>
</body>
</html>

