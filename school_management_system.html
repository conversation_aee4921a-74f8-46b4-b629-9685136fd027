<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المدرسة المتطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .nav-tabs {
            background: #f8f9fa;
            border-bottom: 3px solid #667eea;
        }
        
        .nav-tabs .nav-link {
            color: #495057;
            font-weight: 600;
            border: none;
            border-radius: 0;
            padding: 15px 25px;
            transition: all 0.3s ease;
        }
        
        .nav-tabs .nav-link:hover {
            background: #e9ecef;
            color: #667eea;
        }
        
        .nav-tabs .nav-link.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .tab-content {
            padding: 30px;
            min-height: 600px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }
        
        .table tbody tr {
            transition: all 0.3s ease;
        }
        
        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.1);
        }
        
        .badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .modal-content {
            border-radius: 20px;
            border: none;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        .search-box {
            background: white;
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 12px 20px;
            margin-bottom: 20px;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
        
        .btn-sm {
            padding: 5px 10px;
            border-radius: 8px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
            }
            
            .tab-content {
                padding: 15px;
            }
            
            .nav-tabs .nav-link {
                padding: 10px 15px;
                font-size: 0.9rem;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .notification {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            min-width: 300px;
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="bi bi-mortarboard-fill me-3"></i>نظام إدارة المدرسة المتطور</h1>
                <p class="mb-0">إدارة شاملة ومتطورة لجميع شؤون المدرسة</p>
                <small id="current-date"></small>
            </div>
            
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button">
                        <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="students-tab" data-bs-toggle="tab" data-bs-target="#students" type="button">
                        <i class="bi bi-mortarboard me-2"></i>الطلاب
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="teachers-tab" data-bs-toggle="tab" data-bs-target="#teachers" type="button">
                        <i class="bi bi-person-badge me-2"></i>المعلمون
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="classes-tab" data-bs-toggle="tab" data-bs-target="#classes" type="button">
                        <i class="bi bi-book me-2"></i>الفصول
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" type="button">
                        <i class="bi bi-calendar-check me-2"></i>الحضور
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button">
                        <i class="bi bi-gear me-2"></i>الإعدادات
                    </button>
                </li>
            </ul>
            
            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">
                <!-- Dashboard Tab -->
                <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <i class="bi bi-mortarboard fs-1 mb-3"></i>
                                <h3 id="total-students">0</h3>
                                <p>إجمالي الطلاب</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <i class="bi bi-person-badge fs-1 mb-3"></i>
                                <h3 id="total-teachers">0</h3>
                                <p>إجمالي المعلمين</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <i class="bi bi-book fs-1 mb-3"></i>
                                <h3 id="total-classes">0</h3>
                                <p>إجمالي الفصول</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <i class="bi bi-calendar-check fs-1 mb-3"></i>
                                <h3 id="attendance-rate">0%</h3>
                                <p>معدل الحضور اليوم</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-activity me-2"></i>الأنشطة الأخيرة</h5>
                                </div>
                                <div class="card-body">
                                    <div id="recent-activities">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="bg-success rounded-circle p-2 me-3">
                                                <i class="bi bi-check-circle text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">مرحباً بك في نظام إدارة المدرسة</h6>
                                                <small class="text-muted">النظام جاهز للاستخدام</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Students Tab -->
                <div class="tab-pane fade" id="students" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3><i class="bi bi-mortarboard me-2"></i>إدارة الطلاب</h3>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                            <i class="bi bi-plus-circle me-2"></i>إضافة طالب جديد
                        </button>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <input type="text" class="form-control search-box" id="student-search" placeholder="البحث عن طالب...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="student-grade-filter">
                                <option value="">جميع الصفوف</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="student-class-filter">
                                <option value="">جميع الفصول</option>
                            </select>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرقم</th>
                                            <th>الاسم</th>
                                            <th>الصف</th>
                                            <th>الفصل</th>
                                            <th>العمر</th>
                                            <th>الهاتف</th>
                                            <th>معدل الحضور</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="students-table">
                                        <tr>
                                            <td colspan="8" class="empty-state">
                                                <i class="bi bi-inbox"></i>
                                                <h5>لا توجد بيانات طلاب</h5>
                                                <p>ابدأ بإضافة طلاب جدد</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Teachers Tab -->
                <div class="tab-pane fade" id="teachers" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3><i class="bi bi-person-badge me-2"></i>إدارة المعلمين</h3>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                            <i class="bi bi-plus-circle me-2"></i>إضافة معلم جديد
                        </button>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-8">
                            <input type="text" class="form-control search-box" id="teacher-search" placeholder="البحث عن معلم...">
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="teacher-subject-filter">
                                <option value="">جميع المواد</option>
                            </select>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرقم</th>
                                            <th>الاسم</th>
                                            <th>المادة</th>
                                            <th>الهاتف</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>سنوات الخبرة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="teachers-table">
                                        <tr>
                                            <td colspan="7" class="empty-state">
                                                <i class="bi bi-person-plus"></i>
                                                <h5>لا توجد بيانات معلمين</h5>
                                                <p>ابدأ بإضافة معلمين جدد</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Classes Tab -->
                <div class="tab-pane fade" id="classes" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3><i class="bi bi-book me-2"></i>إدارة الفصول</h3>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClassModal">
                            <i class="bi bi-plus-circle me-2"></i>إضافة فصل جديد
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>اسم الفصل</th>
                                            <th>الصف</th>
                                            <th>المعلم المسؤول</th>
                                            <th>رقم الغرفة</th>
                                            <th>السعة</th>
                                            <th>عدد الطلاب</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="classes-table">
                                        <tr>
                                            <td colspan="7" class="empty-state">
                                                <i class="bi bi-building"></i>
                                                <h5>لا توجد فصول دراسية</h5>
                                                <p>ابدأ بإضافة فصول جديدة</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attendance Tab -->
                <div class="tab-pane fade" id="attendance" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3><i class="bi bi-calendar-check me-2"></i>نظام الحضور والغياب</h3>
                        <div>
                            <input type="date" class="form-control d-inline-block" id="attendance-date" style="width: auto;">
                            <button class="btn btn-primary ms-2" onclick="saveAttendance()">
                                <i class="bi bi-save me-2"></i>حفظ الحضور
                            </button>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <select class="form-select" id="attendance-class-filter">
                                <option value="">اختر الفصل</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <button class="btn btn-success btn-sm" onclick="markAllPresent()">
                                    <i class="bi bi-check-all me-1"></i>تحديد الكل حاضر
                                </button>
                                <button class="btn btn-warning btn-sm" onclick="markAllAbsent()">
                                    <i class="bi bi-x-circle me-1"></i>تحديد الكل غائب
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div id="attendance-list">
                                <div class="empty-state">
                                    <i class="bi bi-calendar-x"></i>
                                    <h5>اختر فصل لعرض قائمة الطلاب</h5>
                                    <p>حدد فصل من القائمة أعلاه</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane fade" id="settings" role="tabpanel">
                    <h3><i class="bi bi-gear me-2"></i>إعدادات النظام</h3>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">معلومات المدرسة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">اسم المدرسة</label>
                                        <input type="text" class="form-control" id="school-name" value="مدرسة المستقبل الابتدائية">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">العام الدراسي</label>
                                        <input type="text" class="form-control" id="academic-year" value="2024-2025">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">هاتف المدرسة</label>
                                        <input type="text" class="form-control" id="school-phone" value="011-1234567">
                                    </div>
                                    <button class="btn btn-primary" onclick="saveSettings()">
                                        <i class="bi bi-save me-2"></i>حفظ الإعدادات
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">إدارة البيانات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-info" onclick="generateSampleData()">
                                            <i class="bi bi-database-add me-2"></i>إنشاء بيانات تجريبية
                                        </button>
                                        <button class="btn btn-success" onclick="exportData()">
                                            <i class="bi bi-download me-2"></i>تصدير البيانات
                                        </button>
                                        <input type="file" id="import-file" accept=".json" style="display: none;" onchange="importData()">
                                        <button class="btn btn-warning" onclick="document.getElementById('import-file').click()">
                                            <i class="bi bi-upload me-2"></i>استيراد البيانات
                                        </button>
                                        <button class="btn btn-danger" onclick="clearAllData()">
                                            <i class="bi bi-trash me-2"></i>مسح جميع البيانات
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="mb-0">إحصائيات النظام</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <h4 id="system-students">0</h4>
                                            <small>طالب مسجل</small>
                                        </div>
                                        <div class="col-6">
                                            <h4 id="system-teachers">0</h4>
                                            <small>معلم مسجل</small>
                                        </div>
                                        <div class="col-6 mt-2">
                                            <h4 id="system-classes">0</h4>
                                            <small>فصل دراسي</small>
                                        </div>
                                        <div class="col-6 mt-2">
                                            <h4 id="system-attendance">0</h4>
                                            <small>سجل حضور</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Add Student Modal -->
    <div class="modal fade" id="addStudentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة طالب جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="student-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الطالب *</label>
                                <input type="text" class="form-control" id="student-id" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="student-name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الصف الدراسي *</label>
                                <select class="form-select" id="student-grade" required>
                                    <option value="">اختر الصف</option>
                                    <option value="الأول الابتدائي">الأول الابتدائي</option>
                                    <option value="الثاني الابتدائي">الثاني الابتدائي</option>
                                    <option value="الثالث الابتدائي">الثالث الابتدائي</option>
                                    <option value="الرابع الابتدائي">الرابع الابتدائي</option>
                                    <option value="الخامس الابتدائي">الخامس الابتدائي</option>
                                    <option value="السادس الابتدائي">السادس الابتدائي</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفصل *</label>
                                <select class="form-select" id="student-class" required>
                                    <option value="">اختر الفصل</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">العمر *</label>
                                <input type="number" class="form-control" id="student-age" min="5" max="18" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الجنس *</label>
                                <select class="form-select" id="student-gender" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="ذكر">ذكر</option>
                                    <option value="أنثى">أنثى</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">هاتف ولي الأمر *</label>
                                <input type="tel" class="form-control" id="student-phone" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="student-email">
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea class="form-control" id="student-address" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="addStudent()">إضافة الطالب</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Teacher Modal -->
    <div class="modal fade" id="addTeacherModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة معلم جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="teacher-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم المعلم *</label>
                                <input type="text" class="form-control" id="teacher-id" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="teacher-name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المادة التخصصية *</label>
                                <select class="form-select" id="teacher-subject" required>
                                    <option value="">اختر المادة</option>
                                    <option value="اللغة العربية">اللغة العربية</option>
                                    <option value="الرياضيات">الرياضيات</option>
                                    <option value="العلوم">العلوم</option>
                                    <option value="التربية الإسلامية">التربية الإسلامية</option>
                                    <option value="التربية البدنية">التربية البدنية</option>
                                    <option value="التربية الفنية">التربية الفنية</option>
                                    <option value="اللغة الإنجليزية">اللغة الإنجليزية</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" id="teacher-phone" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="teacher-email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سنوات الخبرة *</label>
                                <input type="number" class="form-control" id="teacher-experience" min="0" max="40" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المؤهل العلمي</label>
                                <select class="form-select" id="teacher-qualification">
                                    <option value="">اختر المؤهل</option>
                                    <option value="دبلوم">دبلوم</option>
                                    <option value="بكالوريوس">بكالوريوس</option>
                                    <option value="ماجستير">ماجستير</option>
                                    <option value="دكتوراه">دكتوراه</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الراتب الشهري</label>
                                <input type="number" class="form-control" id="teacher-salary" min="0">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="addTeacher()">إضافة المعلم</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Class Modal -->
    <div class="modal fade" id="addClassModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة فصل جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="class-form">
                        <div class="mb-3">
                            <label class="form-label">اسم الفصل *</label>
                            <input type="text" class="form-control" id="class-name" required placeholder="مثال: 1أ">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الصف الدراسي *</label>
                            <select class="form-select" id="class-grade" required>
                                <option value="">اختر الصف</option>
                                <option value="الأول الابتدائي">الأول الابتدائي</option>
                                <option value="الثاني الابتدائي">الثاني الابتدائي</option>
                                <option value="الثالث الابتدائي">الثالث الابتدائي</option>
                                <option value="الرابع الابتدائي">الرابع الابتدائي</option>
                                <option value="الخامس الابتدائي">الخامس الابتدائي</option>
                                <option value="السادس الابتدائي">السادس الابتدائي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المعلم المسؤول *</label>
                            <select class="form-select" id="class-teacher" required>
                                <option value="">اختر المعلم</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الغرفة *</label>
                            <input type="text" class="form-control" id="class-room" required placeholder="مثال: 101">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">السعة القصوى *</label>
                            <input type="number" class="form-control" id="class-capacity" min="10" max="50" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="addClass()">إضافة الفصل</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let students = JSON.parse(localStorage.getItem('school_students') || '[]');
        let teachers = JSON.parse(localStorage.getItem('school_teachers') || '[]');
        let classes = JSON.parse(localStorage.getItem('school_classes') || '[]');
        let attendance = JSON.parse(localStorage.getItem('school_attendance') || '{}');
        let settings = JSON.parse(localStorage.getItem('school_settings') || '{"schoolName": "مدرسة المستقبل الابتدائية", "academicYear": "2024-2025", "schoolPhone": "011-1234567"}');

        // حفظ البيانات
        function saveData() {
            localStorage.setItem('school_students', JSON.stringify(students));
            localStorage.setItem('school_teachers', JSON.stringify(teachers));
            localStorage.setItem('school_classes', JSON.stringify(classes));
            localStorage.setItem('school_attendance', JSON.stringify(attendance));
            localStorage.setItem('school_settings', JSON.stringify(settings));
        }

        // تحديث لوحة التحكم
        function updateDashboard() {
            document.getElementById('total-students').textContent = students.length;
            document.getElementById('total-teachers').textContent = teachers.length;
            document.getElementById('total-classes').textContent = classes.length;

            // حساب معدل الحضور اليوم
            const today = new Date().toISOString().split('T')[0];
            const todayAttendance = attendance[today] || {};
            const totalStudents = students.length;
            const presentStudents = Object.values(todayAttendance).filter(status => status === 'حاضر').length;
            const attendanceRate = totalStudents > 0 ? Math.round((presentStudents / totalStudents) * 100) : 0;

            document.getElementById('attendance-rate').textContent = attendanceRate + '%';

            // تحديث التاريخ
            document.getElementById('current-date').textContent = new Date().toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            // تحديث إحصائيات النظام
            document.getElementById('system-students').textContent = students.length;
            document.getElementById('system-teachers').textContent = teachers.length;
            document.getElementById('system-classes').textContent = classes.length;

            let attendanceCount = 0;
            Object.values(attendance).forEach(dayData => {
                attendanceCount += Object.keys(dayData).length;
            });
            document.getElementById('system-attendance').textContent = attendanceCount;
        }

        // إضافة طالب جديد
        function addStudent() {
            const student = {
                id: Date.now(),
                studentId: document.getElementById('student-id').value,
                name: document.getElementById('student-name').value,
                grade: document.getElementById('student-grade').value,
                class: document.getElementById('student-class').value,
                age: parseInt(document.getElementById('student-age').value),
                gender: document.getElementById('student-gender').value,
                phone: document.getElementById('student-phone').value,
                email: document.getElementById('student-email').value,
                address: document.getElementById('student-address').value,
                createdAt: new Date().toISOString()
            };

            // التحقق من عدم تكرار الرقم
            if (students.some(s => s.studentId === student.studentId)) {
                showNotification('رقم الطالب موجود مسبقاً!', 'danger');
                return;
            }

            students.push(student);
            saveData();
            updateStudentsTable();
            updateDashboard();
            updateFilters();

            // إغلاق النافذة وإعادة تعيين النموذج
            bootstrap.Modal.getInstance(document.getElementById('addStudentModal')).hide();
            document.getElementById('student-form').reset();

            showNotification('تم إضافة الطالب بنجاح!', 'success');
        }

        // إضافة معلم جديد
        function addTeacher() {
            const teacher = {
                id: Date.now(),
                teacherId: document.getElementById('teacher-id').value,
                name: document.getElementById('teacher-name').value,
                subject: document.getElementById('teacher-subject').value,
                phone: document.getElementById('teacher-phone').value,
                email: document.getElementById('teacher-email').value,
                experience: parseInt(document.getElementById('teacher-experience').value),
                qualification: document.getElementById('teacher-qualification').value,
                salary: parseFloat(document.getElementById('teacher-salary').value) || 0,
                createdAt: new Date().toISOString()
            };

            // التحقق من عدم تكرار الرقم
            if (teachers.some(t => t.teacherId === teacher.teacherId)) {
                showNotification('رقم المعلم موجود مسبقاً!', 'danger');
                return;
            }

            teachers.push(teacher);
            saveData();
            updateTeachersTable();
            updateDashboard();
            updateFilters();

            // إغلاق النافذة وإعادة تعيين النموذج
            bootstrap.Modal.getInstance(document.getElementById('addTeacherModal')).hide();
            document.getElementById('teacher-form').reset();

            showNotification('تم إضافة المعلم بنجاح!', 'success');
        }

        // إضافة فصل جديد
        function addClass() {
            const classData = {
                id: Date.now(),
                name: document.getElementById('class-name').value,
                grade: document.getElementById('class-grade').value,
                teacher: document.getElementById('class-teacher').value,
                room: document.getElementById('class-room').value,
                capacity: parseInt(document.getElementById('class-capacity').value),
                createdAt: new Date().toISOString()
            };

            // التحقق من عدم تكرار اسم الفصل
            if (classes.some(c => c.name === classData.name)) {
                showNotification('اسم الفصل موجود مسبقاً!', 'danger');
                return;
            }

            classes.push(classData);
            saveData();
            updateClassesTable();
            updateDashboard();
            updateFilters();

            // إغلاق النافذة وإعادة تعيين النموذج
            bootstrap.Modal.getInstance(document.getElementById('addClassModal')).hide();
            document.getElementById('class-form').reset();

            showNotification('تم إضافة الفصل بنجاح!', 'success');
        }

        // تحديث جدول الطلاب
        function updateStudentsTable() {
            const tbody = document.getElementById('students-table');

            if (students.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="empty-state">
                            <i class="bi bi-inbox"></i>
                            <h5>لا توجد بيانات طلاب</h5>
                            <p>ابدأ بإضافة طلاب جدد</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            let filteredStudents = students;

            // تطبيق الفلاتر
            const searchTerm = document.getElementById('student-search')?.value.toLowerCase() || '';
            const gradeFilter = document.getElementById('student-grade-filter')?.value || '';
            const classFilter = document.getElementById('student-class-filter')?.value || '';

            if (searchTerm) {
                filteredStudents = filteredStudents.filter(student =>
                    student.name.toLowerCase().includes(searchTerm) ||
                    student.studentId.toLowerCase().includes(searchTerm)
                );
            }

            if (gradeFilter) {
                filteredStudents = filteredStudents.filter(student => student.grade === gradeFilter);
            }

            if (classFilter) {
                filteredStudents = filteredStudents.filter(student => student.class === classFilter);
            }

            filteredStudents.forEach(student => {
                const attendanceRate = calculateStudentAttendanceRate(student.id);
                tbody.innerHTML += `
                    <tr>
                        <td><strong>${student.studentId}</strong></td>
                        <td>${student.name}</td>
                        <td>${student.grade}</td>
                        <td>${student.class || 'غير محدد'}</td>
                        <td>${student.age} سنة</td>
                        <td>${student.phone}</td>
                        <td>
                            <span class="badge ${attendanceRate >= 90 ? 'bg-success' : attendanceRate >= 75 ? 'bg-warning' : 'bg-danger'}">
                                ${attendanceRate}%
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-outline-primary btn-sm" onclick="editStudent(${student.id})" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteStudent(${student.id})" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }

        // تحديث جدول المعلمين
        function updateTeachersTable() {
            const tbody = document.getElementById('teachers-table');

            if (teachers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="empty-state">
                            <i class="bi bi-person-plus"></i>
                            <h5>لا توجد بيانات معلمين</h5>
                            <p>ابدأ بإضافة معلمين جدد</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            let filteredTeachers = teachers;

            // تطبيق الفلاتر
            const searchTerm = document.getElementById('teacher-search')?.value.toLowerCase() || '';
            const subjectFilter = document.getElementById('teacher-subject-filter')?.value || '';

            if (searchTerm) {
                filteredTeachers = filteredTeachers.filter(teacher =>
                    teacher.name.toLowerCase().includes(searchTerm) ||
                    teacher.teacherId.toLowerCase().includes(searchTerm)
                );
            }

            if (subjectFilter) {
                filteredTeachers = filteredTeachers.filter(teacher => teacher.subject === subjectFilter);
            }

            filteredTeachers.forEach(teacher => {
                tbody.innerHTML += `
                    <tr>
                        <td><strong>${teacher.teacherId}</strong></td>
                        <td>${teacher.name}</td>
                        <td>${teacher.subject}</td>
                        <td>${teacher.phone}</td>
                        <td>${teacher.email}</td>
                        <td>${teacher.experience} سنة</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-outline-primary btn-sm" onclick="editTeacher(${teacher.id})" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteTeacher(${teacher.id})" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }

        // تحديث جدول الفصول
        function updateClassesTable() {
            const tbody = document.getElementById('classes-table');

            if (classes.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="empty-state">
                            <i class="bi bi-building"></i>
                            <h5>لا توجد فصول دراسية</h5>
                            <p>ابدأ بإضافة فصول جديدة</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            classes.forEach(classData => {
                const studentsCount = students.filter(s => s.class === classData.name).length;
                tbody.innerHTML += `
                    <tr>
                        <td><strong>${classData.name}</strong></td>
                        <td>${classData.grade}</td>
                        <td>${classData.teacher}</td>
                        <td>${classData.room}</td>
                        <td>${classData.capacity}</td>
                        <td>
                            <span class="badge ${studentsCount >= classData.capacity ? 'bg-danger' : studentsCount >= classData.capacity * 0.8 ? 'bg-warning' : 'bg-success'}">
                                ${studentsCount}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-outline-primary btn-sm" onclick="editClass(${classData.id})" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteClass(${classData.id})" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }

        // حساب معدل حضور الطالب
        function calculateStudentAttendanceRate(studentId) {
            let totalDays = 0;
            let presentDays = 0;

            Object.keys(attendance).forEach(date => {
                if (attendance[date][studentId]) {
                    totalDays++;
                    if (attendance[date][studentId] === 'حاضر') {
                        presentDays++;
                    }
                }
            });

            return totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;
        }

        // تحديث الفلاتر
        function updateFilters() {
            // فلتر الصفوف للطلاب
            const gradeFilter = document.getElementById('student-grade-filter');
            if (gradeFilter) {
                const grades = [...new Set(students.map(s => s.grade))];
                gradeFilter.innerHTML = '<option value="">جميع الصفوف</option>';
                grades.forEach(grade => {
                    gradeFilter.innerHTML += `<option value="${grade}">${grade}</option>`;
                });
            }

            // فلتر الفصول للطلاب
            const classFilter = document.getElementById('student-class-filter');
            if (classFilter) {
                const classNames = [...new Set(classes.map(c => c.name))];
                classFilter.innerHTML = '<option value="">جميع الفصول</option>';
                classNames.forEach(className => {
                    classFilter.innerHTML += `<option value="${className}">${className}</option>`;
                });
            }

            // فلتر المواد للمعلمين
            const subjectFilter = document.getElementById('teacher-subject-filter');
            if (subjectFilter) {
                const subjects = [...new Set(teachers.map(t => t.subject))];
                subjectFilter.innerHTML = '<option value="">جميع المواد</option>';
                subjects.forEach(subject => {
                    subjectFilter.innerHTML += `<option value="${subject}">${subject}</option>`;
                });
            }

            // تحديث قائمة الفصول في نموذج إضافة الطالب
            const studentClassSelect = document.getElementById('student-class');
            if (studentClassSelect) {
                studentClassSelect.innerHTML = '<option value="">اختر الفصل</option>';
                classes.forEach(classData => {
                    studentClassSelect.innerHTML += `<option value="${classData.name}">${classData.name}</option>`;
                });
            }

            // تحديث قائمة المعلمين في نموذج إضافة الفصل
            const classTeacherSelect = document.getElementById('class-teacher');
            if (classTeacherSelect) {
                classTeacherSelect.innerHTML = '<option value="">اختر المعلم</option>';
                teachers.forEach(teacher => {
                    classTeacherSelect.innerHTML += `<option value="${teacher.name}">${teacher.name}</option>`;
                });
            }

            // تحديث قائمة الفصول في نظام الحضور
            const attendanceClassFilter = document.getElementById('attendance-class-filter');
            if (attendanceClassFilter) {
                attendanceClassFilter.innerHTML = '<option value="">اختر الفصل</option>';
                classes.forEach(classData => {
                    attendanceClassFilter.innerHTML += `<option value="${classData.name}">${classData.name}</option>`;
                });
            }
        }

        // تحديث قائمة الحضور
        function updateAttendanceList() {
            const selectedClass = document.getElementById('attendance-class-filter').value;
            const selectedDate = document.getElementById('attendance-date').value;
            const attendanceList = document.getElementById('attendance-list');

            if (!selectedClass) {
                attendanceList.innerHTML = `
                    <div class="empty-state">
                        <i class="bi bi-calendar-x"></i>
                        <h5>اختر فصل لعرض قائمة الطلاب</h5>
                        <p>حدد فصل من القائمة أعلاه</p>
                    </div>
                `;
                return;
            }

            const classStudents = students.filter(s => s.class === selectedClass);

            if (classStudents.length === 0) {
                attendanceList.innerHTML = `
                    <div class="empty-state">
                        <i class="bi bi-person-x"></i>
                        <h5>لا يوجد طلاب في هذا الفصل</h5>
                        <p>أضف طلاب للفصل أولاً</p>
                    </div>
                `;
                return;
            }

            let html = '<div class="row">';

            classStudents.forEach(student => {
                const currentStatus = attendance[selectedDate]?.[student.id] || '';
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">${student.name}</h6>
                                <p class="card-text small text-muted">رقم الطالب: ${student.studentId}</p>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="attendance_${student.id}" id="present_${student.id}" value="حاضر" ${currentStatus === 'حاضر' ? 'checked' : ''}>
                                    <label class="btn btn-outline-success" for="present_${student.id}">حاضر</label>

                                    <input type="radio" class="btn-check" name="attendance_${student.id}" id="late_${student.id}" value="متأخر" ${currentStatus === 'متأخر' ? 'checked' : ''}>
                                    <label class="btn btn-outline-warning" for="late_${student.id}">متأخر</label>

                                    <input type="radio" class="btn-check" name="attendance_${student.id}" id="absent_${student.id}" value="غائب" ${currentStatus === 'غائب' ? 'checked' : ''}>
                                    <label class="btn btn-outline-danger" for="absent_${student.id}">غائب</label>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            attendanceList.innerHTML = html;
        }

        // حفظ الحضور
        function saveAttendance() {
            const selectedDate = document.getElementById('attendance-date').value;
            const selectedClass = document.getElementById('attendance-class-filter').value;

            if (!selectedDate || !selectedClass) {
                showNotification('يرجى اختيار التاريخ والفصل', 'warning');
                return;
            }

            if (!attendance[selectedDate]) {
                attendance[selectedDate] = {};
            }

            const classStudents = students.filter(s => s.class === selectedClass);
            let savedCount = 0;

            classStudents.forEach(student => {
                const selectedStatus = document.querySelector(`input[name="attendance_${student.id}"]:checked`)?.value;
                if (selectedStatus) {
                    attendance[selectedDate][student.id] = selectedStatus;
                    savedCount++;
                }
            });

            saveData();
            updateDashboard();
            showNotification(`تم حفظ حضور ${savedCount} طالب بنجاح!`, 'success');
        }

        // تحديد الكل حاضر
        function markAllPresent() {
            const presentRadios = document.querySelectorAll('input[value="حاضر"]');
            presentRadios.forEach(radio => radio.checked = true);
        }

        // تحديد الكل غائب
        function markAllAbsent() {
            const absentRadios = document.querySelectorAll('input[value="غائب"]');
            absentRadios.forEach(radio => radio.checked = true);
        }

        // وظائف الحذف
        function deleteStudent(id) {
            if (confirm('هل أنت متأكد من حذف هذا الطالب؟\nسيتم حذف جميع بيانات الحضور المرتبطة به.')) {
                students = students.filter(s => s.id !== id);

                // حذف بيانات الحضور المرتبطة
                Object.keys(attendance).forEach(date => {
                    delete attendance[date][id];
                });

                saveData();
                updateStudentsTable();
                updateDashboard();
                showNotification('تم حذف الطالب بنجاح!', 'success');
            }
        }

        function deleteTeacher(id) {
            if (confirm('هل أنت متأكد من حذف هذا المعلم؟')) {
                teachers = teachers.filter(t => t.id !== id);
                saveData();
                updateTeachersTable();
                updateDashboard();
                updateFilters();
                showNotification('تم حذف المعلم بنجاح!', 'success');
            }
        }

        function deleteClass(id) {
            if (confirm('هل أنت متأكد من حذف هذا الفصل؟')) {
                classes = classes.filter(c => c.id !== id);
                saveData();
                updateClassesTable();
                updateDashboard();
                updateFilters();
                showNotification('تم حذف الفصل بنجاح!', 'success');
            }
        }

        // وظائف الإعدادات
        function saveSettings() {
            settings.schoolName = document.getElementById('school-name').value;
            settings.academicYear = document.getElementById('academic-year').value;
            settings.schoolPhone = document.getElementById('school-phone').value;

            saveData();
            showNotification('تم حفظ الإعدادات بنجاح!', 'success');
        }

        function generateSampleData() {
            if (confirm('هل تريد إنشاء بيانات تجريبية؟\nسيتم إضافة معلمين وفصول وطلاب للاختبار.')) {
                // إضافة معلمين تجريبيين
                const sampleTeachers = [
                    { id: Date.now() + 1, teacherId: 'T001', name: 'أحمد محمد علي', subject: 'اللغة العربية', phone: '0501234567', email: '<EMAIL>', experience: 5, qualification: 'بكالوريوس', salary: 8000 },
                    { id: Date.now() + 2, teacherId: 'T002', name: 'فاطمة سالم أحمد', subject: 'الرياضيات', phone: '0507654321', email: '<EMAIL>', experience: 8, qualification: 'ماجستير', salary: 9000 },
                    { id: Date.now() + 3, teacherId: 'T003', name: 'محمد عبدالله سعد', subject: 'العلوم', phone: '0509876543', email: '<EMAIL>', experience: 3, qualification: 'بكالوريوس', salary: 7500 }
                ];

                // إضافة فصول تجريبية
                const sampleClasses = [
                    { id: Date.now() + 1, name: '1أ', grade: 'الأول الابتدائي', teacher: 'أحمد محمد علي', room: '101', capacity: 25 },
                    { id: Date.now() + 2, name: '2ب', grade: 'الثاني الابتدائي', teacher: 'فاطمة سالم أحمد', room: '102', capacity: 30 },
                    { id: Date.now() + 3, name: '3ج', grade: 'الثالث الابتدائي', teacher: 'محمد عبدالله سعد', room: '103', capacity: 28 }
                ];

                // إضافة طلاب تجريبيين
                const sampleStudents = [
                    { id: Date.now() + 1, studentId: 'S001', name: 'علي أحمد محمد', grade: 'الأول الابتدائي', class: '1أ', age: 7, gender: 'ذكر', phone: '0551234567', email: '', address: 'الرياض' },
                    { id: Date.now() + 2, studentId: 'S002', name: 'نورا سعد علي', grade: 'الأول الابتدائي', class: '1أ', age: 6, gender: 'أنثى', phone: '0557654321', email: '', address: 'الرياض' },
                    { id: Date.now() + 3, studentId: 'S003', name: 'خالد محمد سالم', grade: 'الثاني الابتدائي', class: '2ب', age: 8, gender: 'ذكر', phone: '0559876543', email: '', address: 'الرياض' },
                    { id: Date.now() + 4, studentId: 'S004', name: 'مريم عبدالله أحمد', grade: 'الثاني الابتدائي', class: '2ب', age: 7, gender: 'أنثى', phone: '0552468135', email: '', address: 'الرياض' },
                    { id: Date.now() + 5, studentId: 'S005', name: 'يوسف سالم محمد', grade: 'الثالث الابتدائي', class: '3ج', age: 9, gender: 'ذكر', phone: '0558642097', email: '', address: 'الرياض' }
                ];

                // دمج البيانات التجريبية
                teachers.push(...sampleTeachers);
                classes.push(...sampleClasses);
                students.push(...sampleStudents);

                // إنشاء بيانات حضور تجريبية
                const today = new Date();
                for (let i = 7; i >= 1; i--) {
                    const date = new Date(today);
                    date.setDate(date.getDate() - i);
                    const dateStr = date.toISOString().split('T')[0];

                    if (!attendance[dateStr]) attendance[dateStr] = {};

                    sampleStudents.forEach(student => {
                        const random = Math.random();
                        if (random > 0.1) { // 90% حضور
                            attendance[dateStr][student.id] = random > 0.05 ? 'حاضر' : 'متأخر';
                        } else {
                            attendance[dateStr][student.id] = 'غائب';
                        }
                    });
                }

                saveData();
                updateDashboard();
                updateStudentsTable();
                updateTeachersTable();
                updateClassesTable();
                updateFilters();
                showNotification('تم إنشاء البيانات التجريبية بنجاح!', 'success');
            }
        }

        function exportData() {
            const allData = {
                students,
                teachers,
                classes,
                attendance,
                settings,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(allData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `school_data_backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير البيانات بنجاح!', 'success');
        }

        function importData() {
            const fileInput = document.getElementById('import-file');
            const file = fileInput.files[0];

            if (!file) {
                showNotification('يرجى اختيار ملف للاستيراد', 'warning');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);

                    if (confirm('هل أنت متأكد من استيراد البيانات؟\nسيتم استبدال البيانات الحالية.')) {
                        students = importedData.students || [];
                        teachers = importedData.teachers || [];
                        classes = importedData.classes || [];
                        attendance = importedData.attendance || {};
                        settings = importedData.settings || settings;

                        saveData();
                        updateDashboard();
                        updateStudentsTable();
                        updateTeachersTable();
                        updateClassesTable();
                        updateFilters();
                        showNotification('تم استيراد البيانات بنجاح!', 'success');
                    }
                } catch (error) {
                    showNotification('خطأ في قراءة الملف: ' + error.message, 'danger');
                }
            };
            reader.readAsText(file);
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟\nهذا الإجراء لا يمكن التراجع عنه!')) {
                students = [];
                teachers = [];
                classes = [];
                attendance = {};

                saveData();
                updateDashboard();
                updateStudentsTable();
                updateTeachersTable();
                updateClassesTable();
                updateFilters();
                showNotification('تم مسح جميع البيانات!', 'success');
            }
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show notification`;
            notification.innerHTML = `
                <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 4000);
        }

        // إضافة مستمعي الأحداث
        function addEventListeners() {
            // البحث والفلترة
            document.getElementById('student-search')?.addEventListener('input', updateStudentsTable);
            document.getElementById('student-grade-filter')?.addEventListener('change', updateStudentsTable);
            document.getElementById('student-class-filter')?.addEventListener('change', updateStudentsTable);

            document.getElementById('teacher-search')?.addEventListener('input', updateTeachersTable);
            document.getElementById('teacher-subject-filter')?.addEventListener('change', updateTeachersTable);

            // نظام الحضور
            document.getElementById('attendance-class-filter')?.addEventListener('change', updateAttendanceList);
            document.getElementById('attendance-date')?.addEventListener('change', updateAttendanceList);

            // تعيين التاريخ الحالي
            const today = new Date().toISOString().split('T')[0];
            const attendanceDateInput = document.getElementById('attendance-date');
            if (attendanceDateInput) {
                attendanceDateInput.value = today;
            }
        }

        // وظائف مؤقتة للتعديل
        function editStudent(id) { showNotification('ميزة التعديل قيد التطوير', 'info'); }
        function editTeacher(id) { showNotification('ميزة التعديل قيد التطوير', 'info'); }
        function editClass(id) { showNotification('ميزة التعديل قيد التطوير', 'info'); }

        // التهيئة الأولية
        function initializeApp() {
            updateDashboard();
            updateStudentsTable();
            updateTeachersTable();
            updateClassesTable();
            updateFilters();
            addEventListeners();

            // تحميل الإعدادات
            document.getElementById('school-name').value = settings.schoolName || 'مدرسة المستقبل الابتدائية';
            document.getElementById('academic-year').value = settings.academicYear || '2024-2025';
            document.getElementById('school-phone').value = settings.schoolPhone || '011-1234567';
        }

        // تشغيل التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            showNotification('مرحباً بك في نظام إدارة المدرسة المتطور!', 'success');
        });
    </script>
</body>
</html>
